import { useState } from "react";
import { useQ<PERSON>y, useMutation, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Edit, Wrench } from "lucide-react";
import { Doc } from "../../../convex/_generated/dataModel";
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

type McpServer = Doc<"mcpServers">;

function McpServerForm({
  server,
  onSave,
  onClose,
}: {
  server?: McpServer;
  onSave: (data: Partial<McpServer>) => void;
  onClose: () => void;
}) {
  const [name, setName] = useState(server?.name ?? "");
  const [description, setDescription] = useState(server?.description ?? "");
  // Only SSE or HTTP are now selectable in the UI
  const [transportType, setTransportType] = useState<"sse" | "http">(
    (server as any)?.transportType === "http" ? "http" : "sse"
  );
  const [url, setUrl] = useState(server?.url ?? "");
  const [headers, setHeaders] = useState(
    server && (server as any).headers 
      ? JSON.stringify((server as any).headers, null, 2) 
      : ""
  );

  const handleSubmit = () => {
    try {
      const parsedHeaders = headers.trim() ? JSON.parse(headers) : undefined;
      onSave({ 
        name, 
        description, 
        transportType, 
        url, 
        headers: parsedHeaders 
      });
    } catch (error) {
      toast.error("Invalid JSON in headers field");
    }
  };

  return (
    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>
          {server ? "Edit MCP Server" : "Add MCP Server"}
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        <div className="space-y-3">
          <Label htmlFor="mcpName" className="text-sm font-medium">Server Name</Label>
          <Input
            id="mcpName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="My MCP Server"
            className="h-10"
          />
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="mcpTransport" className="text-sm font-medium">Transport Type</Label>
          <Select
            value={transportType}
            onValueChange={(value: "sse" | "http") => setTransportType(value)}
          >
            <SelectTrigger className="h-10">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sse">SSE (Server-Sent Events)</SelectItem>
              <SelectItem value="http">HTTP (REST API)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="mcpUrl" className="text-sm font-medium">Server URL</Label>
          <Input
            id="mcpUrl"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder={transportType === "sse" ? "https://your-server.com/sse" : "https://your-server.com/mcp"}
            className="h-10"
          />
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="mcpDescription" className="text-sm font-medium">Description (Optional)</Label>
          <Textarea
            id="mcpDescription"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Brief description of what this server provides"
            rows={2}
            className="resize-none"
          />
        </div>
        
        <div className="space-y-3">
          <Label htmlFor="mcpHeaders" className="text-sm font-medium">Headers (JSON, Optional)</Label>
          <Textarea
            id="mcpHeaders"
            value={headers}
            onChange={(e) => setHeaders(e.target.value)}
            placeholder='{"Authorization": "Bearer your-token"}'
            rows={3}
            className="resize-none font-mono text-sm"
          />
        </div>
      </div>
      
      <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!name.trim() || !url.trim()}
          className="w-full sm:w-auto"
        >
          {server ? "Update" : "Add"} Server
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}

export function McpSection() {
  const mcpServers = useQuery(api.mcpServers.list) ?? [];
  const addServer = useMutation(api.mcpServers.add);
  const updateServer = useMutation(api.mcpServers.update);
  const removeServer = useMutation(api.mcpServers.remove);
  const updateToolsCache = useMutation(api.mcpServers.updateToolsCache);
  const fetchAvailableTools = useAction(api.ai.getAvailableTools);
  const toggleServer = useMutation(api.mcpServers.toggle);
  const updatePreferences = useMutation(api.preferences.update);
  const preferences = useQuery(api.preferences.get);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingServer, setEditingServer] = useState<McpServer | undefined>();

  const handleSave = async (data: Partial<McpServer>) => {
    try {
      let serverId: any;
      if (editingServer) {
        await updateServer({ id: editingServer._id, ...data });
        serverId = editingServer._id;
        toast.success("MCP server updated.");
      } else {
        serverId = await addServer(data as any);
        toast.success("MCP server added.");
      }

      // Refresh available tools and cache them for this server
      try {
        const toolsConfig: Record<string, any> = await fetchAvailableTools({});
        const serverName = (data.name ?? editingServer?.name ?? "").toLowerCase().replace(/[^a-z0-9]/g, "_");
        const prefix = `mcp_${serverName}_`;
        const toolIds = Object.keys(toolsConfig).filter((id) => id.startsWith(prefix));

        if (toolIds.length > 0) {
          await updateToolsCache({ id: serverId, availableTools: toolIds });
        }
      } catch (toolErr) {
        console.error("Failed to update MCP server tools cache:", toolErr);
      }

      // ensure base tool id is in enabledTools list
      const baseToolId = `mcp_${(data.name ?? editingServer?.name ?? "").toLowerCase().replace(/[^a-z0-9]/g, "_")}`;
      if (preferences) {
        const enabled = preferences.enabledTools ?? [];
        if (!enabled.includes(baseToolId)) {
          await updatePreferences({ enabledTools: [...enabled, baseToolId] });
        }
      }
      setIsFormOpen(false);
      setEditingServer(undefined);
    } catch {
      toast.error("Failed to save MCP server.");
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await removeServer({ id });
      toast.success("MCP server removed.");
    } catch {
      toast.error("Failed to remove MCP server.");
    }
  };

  return (
    <div className="space-y-6">
      <Alert className="shadow-sm">
        <Wrench className="h-4 w-4" />
        <AlertTitle>Model Context Protocol (MCP) Servers</AlertTitle>
        <AlertDescription>
          MCP servers provide additional tools and capabilities to your AI assistant. Connect to servers for things like code execution, database access, and more.
        </AlertDescription>
      </Alert>

      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="text-xl font-semibold">Your MCP Servers</CardTitle>
            <CardDescription className="text-sm text-muted-foreground mt-1">
              Manage your connected MCP servers.
            </CardDescription>
          </div>
          <Button
            onClick={() => {
              setEditingServer(undefined);
              setIsFormOpen(true);
            }}
            size="sm"
            className="text-sm py-2 px-3"
          >
            <Plus className="mr-2 h-4 w-4" /> Add Server
          </Button>
        </CardHeader>
        <CardContent>
          {mcpServers.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No MCP servers configured yet.
            </div>
          ) : (
            <div className="space-y-4">
              {mcpServers.map((server) => (
                <Card key={server._id} className="shadow-sm hover:shadow-md transition-shadow duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <CardTitle className="text-lg font-medium">{server.name}</CardTitle>
                      <CardDescription className="text-sm mt-1">{server.description}</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs uppercase">
                        {server.transportType}
                      </Badge>
                      <Badge variant={server.isEnabled ? "default" : "secondary"} className="text-xs">
                        {server.isEnabled ? "Enabled" : "Disabled"}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingServer(server);
                          setIsFormOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={server.isEnabled ? "destructive" : "default"}
                        size="sm"
                        onClick={() => void toggleServer({ id: server._id })}
                      >
                        {server.isEnabled ? "Disable" : "Enable"}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => void handleDelete(server._id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="text-sm text-muted-foreground">
                      URL: {server.url}
                    </div>
                    {server.availableTools && server.availableTools.length > 0 && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Tools: {server.availableTools.join(", ")}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <McpServerForm
          server={editingServer}
          onSave={(data) => void handleSave(data)}
          onClose={() => setIsFormOpen(false)}
        />
      </Dialog>
    </div>
  );
} 