import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

export function UsageSection() {
  const usage = useQuery(api.usage.get);

  if (!usage) {
    return <div>Loading...</div>;
  }

  const { plan, creditsUsed, creditsLimit, resetDate } = usage;
  const creditsPercentage = (creditsUsed / creditsLimit) * 100;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Plan & Usage</CardTitle>
          <CardDescription>
            You are currently on the{" "}
            <span className="font-semibold capitalize">{plan}</span> plan.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Credits Used</span>
              <span className="text-sm text-muted-foreground">
                {creditsUsed.toLocaleString()} / {creditsLimit.toLocaleString()}
              </span>
            </div>
            <Progress value={creditsPercentage} />
            <p className="text-xs text-muted-foreground">
              Your credits will reset on{" "}
              {new Date(resetDate).toLocaleDateString()}.
            </p>
          </div>
          <Separator />
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-semibold">Need more power?</h4>
              <p className="text-sm text-muted-foreground">Upgrade to a Pro plan for unlimited credits and more features.</p>
            </div>
            <Button>Upgrade Plan</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 