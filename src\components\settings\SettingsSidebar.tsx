import {
  User,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  KeyRound,
  BarChart4,
  Database,
  Server,
  Workflow,
  Settings,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export const navItems = [
  { id: "account", label: "Account", icon: User },
  { id: "appearance", label: "Appearance", icon: Palette },
  { id: "ai", label: "AI", icon: Bo<PERSON> },
  { id: "apiKeys", label: "API Keys", icon: KeyRound },
  { id: "customProviders", label: "Custom Providers", icon: Settings },
  { id: "data", label: "Data", icon: Database },
  { id: "mcp", label: "MCP", icon: Server },
  { id: "n8n", label: "n8n", icon: Workflow },
  { id: "usage", label: "Usage", icon: BarChart4 },
];

interface SettingsSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  className?: string;
}

export function SettingsSidebar({
  activeSection,
  onSectionChange,
  className,
}: SettingsSidebarProps) {
  return (
    <nav className={cn("flex flex-col gap-1", className)}>
      {navItems.map((item) => (
        <Button
          key={item.id}
          variant={activeSection === item.id ? "secondary" : "ghost"}
          className="justify-start gap-3 py-2 px-3 text-sm font-medium transition-all duration-200 hover:shadow-sm"
          onClick={() => onSectionChange(item.id)}
        >
          <item.icon className="size-5 shrink-0" />
          <span>{item.label}</span>
        </Button>
      ))}
    </nav>
  );
} 