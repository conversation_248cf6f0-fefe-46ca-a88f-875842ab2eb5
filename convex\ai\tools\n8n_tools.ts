"use node";

import { z } from "zod";
import fetch from "node-fetch";
import type { Doc } from "../../_generated/dataModel";

/**
 * Generic n8n public-API tools
 *
 * These tools work against **any** n8n server. The caller supplies
 *   – apiUrl  (base URL, without trailing slash)
 *   – apiKey  (optional – leave empty for open instances)
 *
 * NOTE: n8n’s public REST API is only available on paid Cloud plans or on
 * self-hosted instances where the API hasn’t been disabled. If the API is
 * disabled the tools will throw a clear error.
 */

// ---------------------------------------------------------------------------
// Shared helpers
// ---------------------------------------------------------------------------

const BaseParams = z.object({
  /** Base URL of the n8n instance, e.g. https://n8n.example.com */
  apiUrl: z.string(),
  /** n8n API key for authentication */
  apiKey: z.string(),
});

function headers(apiKey?: string): Record<string, string> {
  const h: Record<string, string> = { Accept: "application/json" };
  if (apiKey) h["X-N8N-API-KEY"] = apiKey;
  return h;
}

function sanitizeUrl(url: string) {
  return url.replace(/\/+$/, "");
}

async function fetchJson<T>(url: string, apiKey?: string): Promise<T> {
  const res = await fetch(url, { headers: headers(apiKey) } as any);
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`${res.status} ${res.statusText}: ${text}`);
  }
  return (await res.json()) as T;
}

// ---------------------------------------------------------------------------
// Tool implementations
// ---------------------------------------------------------------------------

async function workflowList(params: z.infer<typeof BaseParams>) {
  const { apiUrl, apiKey } = params;
  const base = sanitizeUrl(apiUrl);
  const data = await fetchJson<{ data: any[] }>(
    `${base}/api/v1/workflows`,
    apiKey
  );
  return data.data;
}

async function workflowRead(
  params: z.infer<typeof BaseParams> & { workflowId: string }
) {
  const { apiUrl, apiKey, workflowId } = params;
  const base = sanitizeUrl(apiUrl);
  return await fetchJson<any>(`${base}/api/v1/workflows/${workflowId}`, apiKey);
}

async function workflowActivate(
  params: z.infer<typeof BaseParams> & { workflowId: string }
) {
  const { apiUrl, apiKey, workflowId } = params;
  const base = sanitizeUrl(apiUrl);
  const res = await fetch(`${base}/api/v1/workflows/${workflowId}/activate`, {
    method: "POST",
    headers: headers(apiKey),
  } as any);
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`${res.status} ${res.statusText}: ${text}`);
  }
  return { success: true };
}

async function workflowDeactivate(
  params: z.infer<typeof BaseParams> & { workflowId: string }
) {
  const { apiUrl, apiKey, workflowId } = params;
  const base = sanitizeUrl(apiUrl);
  const res = await fetch(`${base}/api/v1/workflows/${workflowId}/deactivate`, {
    method: "POST",
    headers: headers(apiKey),
  } as any);
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`${res.status} ${res.statusText}: ${text}`);
  }
  return { success: true };
}

async function executionList(
  params: z.infer<typeof BaseParams> & { limit?: number; workflowId?: string }
) {
  const { apiUrl, apiKey, limit = 20, workflowId } = params;
  const base = sanitizeUrl(apiUrl);
  const qs = new URLSearchParams();
  qs.set("limit", String(limit));
  if (workflowId) qs.set("workflowId", workflowId);
  const url = `${base}/api/v1/executions?${qs.toString()}`;
  const data = await fetchJson<{ data: any[] }>(url, apiKey);
  return data.data;
}

// Start a new execution (accepts arbitrary payload)
async function workflowExecute(
  params: z.infer<typeof BaseParams> & {
    workflowId: string;
    payload?: Record<string, any>;
  }
) {
  const { apiUrl, apiKey, workflowId, payload } = params;
  const base = sanitizeUrl(apiUrl);
  const res = await fetch(`${base}/api/v1/workflows/${workflowId}/executions`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      ...headers(apiKey),
    } as any,
    body: JSON.stringify(payload ?? {}),
  } as any);

  if (!res.ok) {
    const text = await res.text();
    throw new Error(`${res.status} ${res.statusText}: ${text}`);
  }

  // n8n returns { data: { id, startedAt, ... } }
  const data = (await res.json()) as any;
  return data;
}

// ---------------------------------------------------------------------------
// Public factory
// ---------------------------------------------------------------------------

export function createN8nGenericTools() {
  const tools: Record<string, any> = {
    n8n_workflow_list: {
      description: "List all workflows on the specified n8n server",
      parameters: BaseParams,
      execute: workflowList,
    },
    n8n_workflow_read: {
      description: "Get a workflow’s details by id",
      parameters: BaseParams.extend({ workflowId: z.string() }),
      execute: workflowRead,
    },
    n8n_workflow_activate: {
      description: "Activate a workflow by id",
      parameters: BaseParams.extend({ workflowId: z.string() }),
      execute: workflowActivate,
    },
    n8n_workflow_deactivate: {
      description: "Deactivate a workflow by id",
      parameters: BaseParams.extend({ workflowId: z.string() }),
      execute: workflowDeactivate,
    },
    n8n_execution_list: {
      description: "List recent executions (optionally filter by workflow)",
      parameters: BaseParams.extend({
        limit: z.number().optional().describe("Max number of executions"),
        workflowId: z.string().optional(),
      }),
      execute: executionList,
    },

    n8n_workflow_execute: {
      description: "Start a workflow execution and return execution info",
      parameters: BaseParams.extend({
        workflowId: z.string(),
        payload: z.record(z.string(), z.any()).optional(),
      }),
      execute: workflowExecute,
    },
  };

  return tools;
}

// ---------------------------------------------------------------------------
// Metadata for UI
// ---------------------------------------------------------------------------

export function getN8nGenericToolInfo() {
  return [
    {
      id: "n8n_workflow_list",
      name: "n8n: List Workflows",
      description: "List all workflows on a server",
      category: "n8n",
    },
    {
      id: "n8n_workflow_read",
      name: "n8n: Read Workflow",
      description: "Read a single workflow by id",
      category: "n8n",
    },
    {
      id: "n8n_workflow_activate",
      name: "n8n: Activate Workflow",
      description: "Activate a workflow by id",
      category: "n8n",
    },
    {
      id: "n8n_workflow_deactivate",
      name: "n8n: Deactivate Workflow",
      description: "Deactivate a workflow by id",
      category: "n8n",
    },
    {
      id: "n8n_execution_list",
      name: "n8n: List Executions",
      description: "List recent executions (optionally filtered)",
      category: "n8n",
    },
    {
      id: "n8n_workflow_execute",
      name: "n8n: Execute Workflow",
      description: "Trigger a workflow execution with an optional payload",
      category: "n8n",
    },
  ];
}

// ---------------------------------------------------------------------------
// Server-bound tool factory (pre-fills apiUrl & apiKey)
// ---------------------------------------------------------------------------

function safeName(name: string) {
  return name.toLowerCase().replace(/[^a-z0-9]/g, "_");
}

export function createN8nServerTools(servers: Doc<"n8nServers">[]) {
  const tools: Record<string, any> = {};

  servers
    .filter((s) => s.isEnabled)
    .forEach((server) => {
      const nameSlug = safeName(server.name);
      const baseParams = {
        apiUrl: server.apiUrl,
        apiKey: server.apiKey ?? "",
      };

      const prefix = `n8n_${nameSlug}`;

      // List Workflows – no additional params
      tools[`${prefix}_workflow_list`] = {
        description: `[${server.name}] List all workflows`,
        parameters: z.object({}),
        execute: () => workflowList(baseParams),
      };

      // Read workflow
      tools[`${prefix}_workflow_read`] = {
        description: `[${server.name}] Read workflow details by id`,
        parameters: z.object({ workflowId: z.string() }),
        execute: (args: { workflowId: string }) =>
          workflowRead({ ...baseParams, workflowId: args.workflowId }),
      };

      // Activate workflow
      tools[`${prefix}_workflow_activate`] = {
        description: `[${server.name}] Activate workflow by id`,
        parameters: z.object({ workflowId: z.string() }),
        execute: (args: { workflowId: string }) =>
          workflowActivate({ ...baseParams, workflowId: args.workflowId }),
      };

      // Deactivate workflow
      tools[`${prefix}_workflow_deactivate`] = {
        description: `[${server.name}] Deactivate workflow by id`,
        parameters: z.object({ workflowId: z.string() }),
        execute: (args: { workflowId: string }) =>
          workflowDeactivate({ ...baseParams, workflowId: args.workflowId }),
      };

      // List executions
      tools[`${prefix}_execution_list`] = {
        description: `[${server.name}] List recent executions (optionally filter by workflow)`,
        parameters: z.object({
          limit: z.number().optional(),
          workflowId: z.string().optional(),
        }),
        execute: (args: { limit?: number; workflowId?: string }) =>
          executionList({ ...baseParams, ...args }),
      };

      // Execute workflow
      tools[`${prefix}_workflow_execute`] = {
        description: `[${server.name}] Execute workflow with optional payload`,
        parameters: z.object({
          workflowId: z.string(),
          payload: z.record(z.string(), z.any()).optional(),
        }),
        execute: (args: {
          workflowId: string;
          payload?: Record<string, any>;
        }) => workflowExecute({ ...baseParams, ...args }),
      };
    });

  return tools;
}

// ---------------------------------------------------------------------------
// Metadata for UI – one entry per server
// ---------------------------------------------------------------------------

export function getN8nServerToolInfo(servers: Doc<"n8nServers">[]) {
  return servers
    .filter((s) => s.isEnabled)
    .map((server) => ({
      id: `n8n_${safeName(server.name)}`,
      name: `n8n: ${server.name}`,
      description: server.description || `Tools for ${server.name} n8n server`,
      category: "n8n" as const,
      apiUrl: server.apiUrl,
      availableTools: server.availableTools ?? [],
    }));
}
