import { useState } from "react";
import { SettingsPageHeader } from "./settings/SettingsPageHeader";
import {
  SettingsSidebar,
} from "./settings/SettingsSidebar";
import { AccountSection } from "./settings/AccountSection";
import { AppearanceSection } from "./settings/AppearanceSection";
import { AISection } from "./settings/AISection";
import { ApiKeysSection } from "./settings/ApiKeysSection";
import { DataSection } from "./settings/DataSection";
import { McpSection } from "./settings/McpSection";
import { N8nSection } from "./settings/N8nSection";
import { UsageSection } from "./settings/UsageSection";
import { CustomProvidersSection } from "./settings/CustomProvidersSection";

interface SettingsPageProps {
  onBack: () => void;
}

const sectionComponents: Record<string, React.ComponentType> = {
  account: AccountSection,
  appearance: AppearanceSection,
  ai: AISection,
  apiKeys: ApiKeysSection,
  customProviders: CustomProvidersSection,
  data: DataSection,
  mcp: McpSection,
  n8n: N8nSection,
  usage: UsageSection,
};

export function SettingsPage({ onBack }: SettingsPageProps) {
  const [activeSection, setActiveSection] = useState<keyof typeof sectionComponents>(
    "ai",
  );

  const ActiveSectionComponent = sectionComponents[activeSection];

  return (
    <div className="flex h-full flex-col bg-muted/40">
      <SettingsPageHeader onBack={onBack} />
      <main className="flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-4 p-4 md:gap-8 md:p-10">
        <div className="mx-auto grid w-full max-w-6xl gap-2">
          <h1 className="text-3xl font-semibold">Settings</h1>
        </div>
        <div className="mx-auto grid w-full max-w-6xl items-start gap-6 md:grid-cols-[180px_1fr] lg:grid-cols-[250px_1fr]">
          <nav
            className="grid gap-4 text-sm text-muted-foreground"
          >
             <SettingsSidebar
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />
          </nav>
          <div className="grid gap-6">
             {ActiveSectionComponent && <ActiveSectionComponent />}
          </div>
        </div>
      </main>
    </div>
  );
}