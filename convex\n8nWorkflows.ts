import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

// List user's n8n workflows
export const list = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("n8nWorkflows")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
  },
});

// Get enabled n8n workflows
export const listEnabled = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("n8nWorkflows")
      .withIndex("by_user_enabled", (q) =>
        q.eq("userId", userId).eq("isEnabled", true)
      )
      .collect();
  },
});

// Add a new n8n workflow
export const add = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    triggerType: v.union(v.literal("webhook"), v.literal("api")),
    webhookUrl: v.optional(v.string()),
    apiUrl: v.optional(v.string()),
    apiKey: v.optional(v.string()),
    workflowId: v.optional(v.string()),
    parametersSchema: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate required fields based on trigger type
    if (args.triggerType === "webhook" && !args.webhookUrl) {
      throw new Error("Webhook URL is required for webhook trigger type");
    } else if (
      args.triggerType === "api" &&
      (!args.apiUrl || !args.workflowId)
    ) {
      throw new Error(
        "API URL and workflow ID are required for API trigger type"
      );
    }

    return await ctx.db.insert("n8nWorkflows", {
      userId,
      name: args.name,
      description: args.description,
      triggerType: args.triggerType,
      webhookUrl: args.webhookUrl,
      apiUrl: args.apiUrl,
      apiKey: args.apiKey,
      workflowId: args.workflowId,
      parametersSchema: args.parametersSchema,
      isEnabled: true,
      createdAt: Date.now(),
      successCount: 0,
      failureCount: 0,
    });
  },
});

// Update an n8n workflow
export const update = mutation({
  args: {
    id: v.id("n8nWorkflows"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    triggerType: v.optional(v.union(v.literal("webhook"), v.literal("api"))),
    webhookUrl: v.optional(v.string()),
    apiUrl: v.optional(v.string()),
    apiKey: v.optional(v.string()),
    workflowId: v.optional(v.string()),
    parametersSchema: v.optional(v.string()),
    isEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const workflow = await ctx.db.get(args.id);
    if (!workflow || workflow.userId !== userId) {
      throw new Error("n8n workflow not found or access denied");
    }

    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name;
    if (args.description !== undefined) updates.description = args.description;
    if (args.triggerType !== undefined) updates.triggerType = args.triggerType;
    if (args.webhookUrl !== undefined) updates.webhookUrl = args.webhookUrl;
    if (args.apiUrl !== undefined) updates.apiUrl = args.apiUrl;
    if (args.apiKey !== undefined) updates.apiKey = args.apiKey;
    if (args.workflowId !== undefined) updates.workflowId = args.workflowId;
    if (args.parametersSchema !== undefined)
      updates.parametersSchema = args.parametersSchema;
    if (args.isEnabled !== undefined) updates.isEnabled = args.isEnabled;

    return await ctx.db.patch(args.id, updates);
  },
});

// Delete an n8n workflow
export const remove = mutation({
  args: {
    id: v.id("n8nWorkflows"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const workflow = await ctx.db.get(args.id);
    if (!workflow || workflow.userId !== userId) {
      throw new Error("n8n workflow not found or access denied");
    }

    await ctx.db.delete(args.id);
  },
});

// Toggle workflow enabled state
export const toggle = mutation({
  args: {
    id: v.id("n8nWorkflows"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const workflow = await ctx.db.get(args.id);
    if (!workflow || workflow.userId !== userId) {
      throw new Error("n8n workflow not found or access denied");
    }

    await ctx.db.patch(args.id, {
      isEnabled: !workflow.isEnabled,
      lastUsed: workflow.isEnabled ? undefined : Date.now(),
    });
  },
});

// Update workflow usage statistics
export const updateStats = mutation({
  args: {
    id: v.id("n8nWorkflows"),
    success: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const workflow = await ctx.db.get(args.id);
    if (!workflow || workflow.userId !== userId) {
      throw new Error("n8n workflow not found or access denied");
    }

    const updates: any = {
      lastUsed: Date.now(),
    };

    if (args.success) {
      updates.successCount = (workflow.successCount || 0) + 1;
    } else {
      updates.failureCount = (workflow.failureCount || 0) + 1;
    }

    await ctx.db.patch(args.id, updates);
  },
});
