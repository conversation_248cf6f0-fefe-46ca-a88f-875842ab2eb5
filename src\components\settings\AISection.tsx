import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { IMAGE_MODELS, ImageModelId, getModelInfo, formatTokenCount, formatPricing, PROVIDER_CONFIGS } from "@/lib/models";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Doc } from "../../../convex/_generated/dataModel";
import { cn } from "@/lib/utils";

type Preferences = Doc<"userPreferences">;

export function AISection() {
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);

  if (!preferences) {
    return <div>Loading...</div>;
  }

  const {
    aiProvider,
    model,
    temperature,
    systemPrompt,
    useCustomSystemPrompt,
    imageModel,
  } = preferences;

  const update = (updates: Partial<Preferences>) => {
    void updatePreferences(updates);
  };

  const setAiProvider = (value: Preferences["aiProvider"]) => {
    const newProvider = value;
    const newModel = (PROVIDER_CONFIGS as any)[newProvider]?.models?.[0] || "";
    update({ aiProvider: value, model: newModel });
  };
  
  const setModel = (value: Preferences["model"]) => update({ model: value });
  const setTemperature = (value: number[]) => update({ temperature: value[0] });
  const setSystemPrompt = (e: React.ChangeEvent<HTMLTextAreaElement>) =>
    update({ systemPrompt: e.target.value });
  const setUseCustomSystemPrompt = (value: boolean) =>
    update({ useCustomSystemPrompt: value });
  const setImageModel = (value: ImageModelId) => update({ imageModel: value });

  const currentProviderConfig = (PROVIDER_CONFIGS as any)[aiProvider];

  return (
    <div className="space-y-6">
      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">AI Provider & Model</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Choose your AI provider and model for conversations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          {/* Provider Selection */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">AI Provider</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {Object.entries(PROVIDER_CONFIGS).map(([key, config]) => {
                const isSelected = aiProvider === key;
                return (
                  <div
                    key={key}
                    className={cn(
                      "rounded-lg border bg-card text-card-foreground p-3 cursor-pointer transition-all duration-200 hover:shadow-md",
                      isSelected && "bg-accent text-accent-foreground shadow-md",
                    )}
                    onClick={() =>
                      void setAiProvider(key as Preferences["aiProvider"])
                    }
                  >
                    <div className="flex justify-between items-center">
                      <div className="font-semibold">{config.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {config.models.length} models
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Model Selection */}
          {currentProviderConfig?.models?.length > 0 && (
            <div className="space-y-3">
              <Label className="text-base font-semibold">Model Selection</Label>
              <div className="space-y-2 max-h-80 overflow-y-auto pr-2">
                {currentProviderConfig.models.map((modelId: string) => {
                  const modelInfo = getModelInfo(modelId);
                  const isSelected = model === modelId;
                  
                  return (
                    <div
                      key={modelId}
                      className={cn(
                        "rounded-lg border bg-card text-card-foreground p-4 cursor-pointer transition-all duration-200 hover:shadow-md",
                        isSelected && "bg-accent text-accent-foreground shadow-md",
                      )}
                      onClick={() => void setModel(modelId)}
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1">
                          <div className="font-medium">{modelInfo.displayName}</div>
                          {modelInfo.description && (
                            <div className="text-sm text-muted-foreground mt-1">{modelInfo.description}</div>
                          )}
                        </div>
                        <div className="text-right text-xs text-muted-foreground">
                          {modelInfo.pricing && (
                            <div>
                              In: ${formatPricing(modelInfo.pricing.input)}/1M<br/>
                              Out: ${formatPricing(modelInfo.pricing.output)}/1M
                            </div>
                          )}
                          <div className="mt-1">
                            {formatTokenCount(modelInfo.contextWindow)} ctx
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Temperature Control */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Label className="text-base font-semibold">Temperature</Label>
              <span className="text-sm font-mono">{temperature}</span>
            </div>
            <Slider
              value={[temperature]}
              onValueChange={(value) => setTemperature(value)}
              max={2}
              min={0}
              step={0.1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Focused (0.0)</span>
              <span>Balanced (1.0)</span>
              <span>Creative (2.0)</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Image Generation Model */}
      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Image Generation Model</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Choose your preferred model for AI image generation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 p-6">
          <div className="space-y-2">
            {Object.values(IMAGE_MODELS).map((imageModelInfo) => {
              const isSelected = imageModel === imageModelInfo.id;
              
              return (
                <div
                  key={imageModelInfo.id}
                  className={cn(
                    "rounded-lg border bg-card text-card-foreground p-4 cursor-pointer transition-all duration-200 hover:shadow-md",
                    isSelected && "bg-accent text-accent-foreground shadow-md",
                  )}
                  onClick={() => void setImageModel(imageModelInfo.id)}
                >
                  <div className="flex-1">
                    <div className="font-medium">{imageModelInfo.displayName}</div>
                    <div className="text-sm text-muted-foreground mt-1">{imageModelInfo.description}</div>
                    <div className="flex items-center justify-between mt-2">
                      <div className="text-sm">
                        {imageModelInfo.speed} • {imageModelInfo.quality}
                      </div>
                      <div className="text-sm font-medium">
                        ${imageModelInfo.pricing.toFixed(2)} per image
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* System Prompt */}
      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">System Prompt & Personality</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Define the AI's personality and behavior. This prompt is combined with your user instructions to create a unique assistant experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="useCustomSystemPrompt" className="text-base font-semibold">Enable Custom System Prompt</Label>
              <p className="text-sm text-muted-foreground">
                Use a custom system prompt to define the AI's personality and behavior
              </p>
            </div>
            <Switch
              id="useCustomSystemPrompt"
              checked={useCustomSystemPrompt}
              onCheckedChange={(checked) => void setUseCustomSystemPrompt(checked)}
            />
          </div>
          
          {useCustomSystemPrompt && (
            <div className="space-y-3">
              <Label htmlFor="systemPrompt" className="text-base font-semibold">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={systemPrompt ?? ""}
                onChange={(e) => void setSystemPrompt(e)}
                placeholder="Enter your custom system prompt..."
                className="min-h-[150px] resize-none"
                maxLength={2000}
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Define how the AI should behave and respond</span>
                <span>{systemPrompt?.length || 0}/2000</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 