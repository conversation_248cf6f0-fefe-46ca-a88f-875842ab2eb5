//-"use node";

import { httpAction } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { Octokit } from "octokit";

/**
 * HTTP GET /github/oauth/callback – exchanges code for token and stores creds.
 */
export const oauthCallback = httpAction(async (ctx, req) => {
  const url = new URL(req.url);
  const code = url.searchParams.get("code");
  if (!code) return new Response("Missing code", { status: 400 });

  const clientId = process.env.GITHUB_CLIENT_ID;
  const clientSecret = process.env.GITHUB_CLIENT_SECRET;
  if (!clientId || !clientSecret) {
    return new Response("Server not configured", { status: 500 });
  }

  const tokenRes = await fetch("https://github.com/login/oauth/access_token", {
    method: "POST",
    headers: { "Content-Type": "application/json", Accept: "application/json" },
    body: JSON.stringify({
      client_id: clientId,
      client_secret: clientSecret,
      code,
    }),
  });
  const tokenJson = await tokenRes.json();
  const accessToken = tokenJson.access_token as string | undefined;
  if (!accessToken) return new Response("OAuth failed", { status: 400 });

  const octokit = new Octokit({ auth: accessToken });
  const { data: ghUser } = await octokit.rest.users.getAuthenticated();

  const userId = await getAuthUserId(ctx);
  if (userId) {
    await ctx.runMutation(internal.github.storeCredentials, {
      userId,
      githubId: ghUser.id.toString(),
      username: ghUser.login,
      accessToken,
    });
  }

  return new Response(null, { status: 302, headers: { Location: "/repose" } });
});
