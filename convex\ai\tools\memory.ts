"use node";

import { tool } from "ai";
import { z } from "zod";
import { api } from "../../_generated/api";

export function createMemoryTool(ctx: any) {
  return tool({
    description:
      "Store or retrieve important information from long-term conversation memory. Use PROACTIVELY to check for relevant user information before asking questions the user might have already answered in previous conversations. Always search for relevant memories when handling user preferences, personal details, locations, or ongoing projects.",
    parameters: z.object({
      action: z
        .enum(["store", "retrieve", "search"])
        .describe(
          "Whether to store information, retrieve by exact key, or search across all memories for relevant information"
        ),
      key: z
        .string()
        .describe(
          "For store/retrieve: The specific topic/category of information (e.g., 'location', 'preferences', 'project_status'). For search: Keywords to look for in any memory."
        ),
      value: z
        .string()
        .nullable()
        .default("")
        .describe(
          "The important information to store for future conversations (required for store action, null for retrieve/search)"
        ),
    }),
    execute: async ({ action, key, value }): Promise<string> => {
      if (action === "store") {
        if (!value || value.trim() === "") {
          return "Error: Value is required for store action";
        }
        await ctx.runMutation(api.userMemories.add, {
          memory: `${key}: ${value}`,
        });
        return `Stored information for future reference - "${key}": ${value}`;
      } else if (action === "retrieve" && key) {
        try {
          const memories = await ctx.runQuery(api.userMemories.list);
          const memory = memories.find((m: any) =>
            m.memory.toLowerCase().startsWith(`${key.toLowerCase()}:`)
          );
          return memory
            ? `Retrieved stored information for "${key}": ${memory.memory.substring(memory.memory.indexOf(":") + 1).trim()}`
            : `No stored information found for "${key}"`;
        } catch {
          return `No stored information found for "${key}"`;
        }
      } else if (action === "search" && key) {
        try {
          const memories = await ctx.runQuery(api.userMemories.list);
          const matchingMemories = memories.filter((m: any) =>
            m.memory.toLowerCase().includes(key.toLowerCase())
          );

          if (matchingMemories.length > 0) {
            return `Found ${matchingMemories.length} relevant memories:\n${matchingMemories.map((m: any) => `- ${m.memory}`).join("\n")}`;
          }
          return `No memories found containing "${key}"`;
        } catch {
          return `Error searching memories for "${key}"`;
        }
      }
      return "Invalid memory operation - specify 'store', 'retrieve', or 'search' action with appropriate parameters";
    },
  });
}
