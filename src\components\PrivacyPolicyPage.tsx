import { Button } from "./ui/button";

export function PrivacyPolicyPage() {
  const handleBack = () => {
    window.history.back();
  };

  return (
    <div className="py-16 px-4 max-w-3xl mx-auto">
      <h1 className="text-4xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
        Privacy Policy
      </h1>
      <p className="text-muted-foreground leading-relaxed mb-6">
        This is a placeholder privacy policy. Replace this text with your actual privacy
        practices, data retention policies, and user rights statements.
      </p>
      <h2 className="text-2xl font-bold mb-4">1. Information We Collect</h2>
      <p className="text-muted-foreground mb-4">
        Describe the types of information your application collects, such as usage data or
        contact details.
      </p>
      <h2 className="text-2xl font-bold mb-4">2. How We Use Information</h2>
      <p className="text-muted-foreground mb-4">
        Explain how collected information is processed and utilized.
      </p>
      <h2 className="text-2xl font-bold mb-4">3. Your Choices & Rights</h2>
      <p className="text-muted-foreground mb-4">
        Inform users how they can access, update, or delete their information.
      </p>
      <Button onClick={handleBack}>Back</Button>
    </div>
  );
} 