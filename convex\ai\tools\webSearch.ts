"use node";

import { tool } from "ai";
import { z } from "zod";
import { performExaSearch, performExaResearch } from "../utils/search";
import { api } from "../../_generated/api";

export function createWebSearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run a **quick web search** for up-to-date facts, headlines or links. Returns rich snippets and images when available. Returns up to 20 rich results with thumbnails when available.\n\nWhen to use:\n• You need recent news, live data, or external references.\n• You want a list of sources to decide which ones to read in full (via \`url_fetch\`).\n\nAfter calling this, usually follow-up with \`url_fetch\` on 1-3 of the most relevant links if the snippets are insufficient.`,
    parameters: z.object({
      query: z
        .string()
        .describe("The search query for current/real-time information"),
    }),
    execute: async ({ query }): Promise<string> => {
      return await performExaSearch(ctx, query, "advanced", !usingUser<PERSON>ey);
    },
  });
}

export function createResearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run Exa.ai **autonomous deep research**. This tool performs comprehensive research on a given topic or question.

USAGE: You MUST call this tool with an 'instructions' parameter containing your research question.

Example calls:
- research({ instructions: "artificial intelligence trends in 2024" })
- research({ instructions: "impact of climate change on agriculture" })
- research({ instructions: "latest developments in quantum computing" })

Before calling, send a message telling the user that you are starting research.`,
    parameters: z.object({
      instructions: z
        .string()
        .default("")
        .describe(
          "The research question or topic you want to investigate. This must be a meaningful question or topic, not empty."
        ),
    }),
    execute: async (params): Promise<string> => {
      try {
        // Handle cases where params might be undefined or empty
        const { instructions = "" } = params || {};

        // Log the received parameters for debugging
        console.log(`Research tool called with params:`, params);
        console.log(`Instructions extracted:`, instructions);

        // Additional validation
        if (
          !instructions ||
          typeof instructions !== "string" ||
          instructions.trim() === ""
        ) {
          return "Error: Research tool requires an 'instructions' parameter with your research question or topic. Please provide a specific research question.";
        }

        const cleanInstructions = instructions.trim();
        if (cleanInstructions.length < 3) {
          return "Error: Research instructions must be at least 3 characters long. Please provide a meaningful research question.";
        }

        console.log(
          `Starting research with instructions: ${cleanInstructions}`
        );
        return await performExaResearch(ctx, cleanInstructions);
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.error(`Research tool error:`, error);
        return `Research execution error: ${errorMsg}`;
      }
    },
  });
}

export function createDeepSearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run an **in-depth multi-query search**. Best for gathering multiple perspectives or when a single search isn’t enough. It issues several focused searches and merges up to 20 results per query.\n\nWhen to use:\n• You must gather perspectives from many sources, or the single web search didn't yield enough detail.\n\nTypical follow-up: use \`url_fetch\` on promising links to extract full content or facts.`,
    parameters: z.object({
      query: z
        .string()
        .describe("The main research topic requiring current information"),
      related_queries: z
        .array(z.string())
        .describe(
          "Additional specific research angles for comprehensive coverage"
        ),
    }),
    execute: async ({ query, related_queries = [] }): Promise<string> => {
      // Only increment search usage if using built-in keys
      if (!usingUserKey) {
        // Deep search uses additional search quota
        await ctx.runMutation(api.usage.incrementSearches);
        await ctx.runMutation(api.usage.incrementSearches);
      }

      const allQueries = [query, ...related_queries.slice(0, 3)]; // Limit to 4 total queries
      const searchPromises = allQueries.map((q) =>
        performExaSearch(ctx, q, "advanced", !usingUserKey)
      );
      const results = await Promise.all(searchPromises);

      let combinedResults = `Deep search results for "${query}":\n\n`;
      results.forEach((result, index) => {
        combinedResults += `=== Results for "${allQueries[index]}" ===\n${result}\n\n`;
      });

      return combinedResults;
    },
  });
}
