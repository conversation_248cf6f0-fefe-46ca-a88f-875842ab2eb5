"use node";

import { tool } from "ai";
import { z } from "zod";
import { getWeatherData } from "../utils/weather";

export function createWeatherTool(ctx: any) {
  return tool({
    description:
      "Get current weather conditions or a forecast for a specific location. Use when users ask for the weather. If they ask for a future day, provide the date.",
    parameters: z.object({
      location: z
        .string()
        .describe("The city, region, or location for the weather report."),
      date: z
        .string()
        .optional()
        .describe(
          "The desired date for a forecast, in YYYY-MM-DD format. Leave empty or omit for current weather."
        ),
    }),
    execute: async ({ location, date }): Promise<string> => {
      // Treat undefined date as current weather
      const actualDate = date || undefined;
      try {
        return await getWeatherData(ctx, location, actualDate);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        return `Unable to get weather information for ${location}. Error: ${errorMessage}`;
      }
    },
  });
}
