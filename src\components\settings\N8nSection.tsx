import { useState } from "react";
import { useQ<PERSON>y, useMutation, useAction } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Edit, Server, CheckCircle2, XCircle } from "lucide-react";
import { Doc } from "../../../convex/_generated/dataModel";
import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";

type N8nServer = Doc<"n8nServers">;

function N8nServerForm({
  server,
  onSave,
  onClose,
}: {
  server?: N8nServer;
  onSave: (data: Partial<N8nServer>) => void;
  onClose: () => void;
}) {
  const [name, setName] = useState(server?.name ?? "");
  const [description, setDescription] = useState(server?.description ?? "");
  const [apiUrl, setApiUrl] = useState(server?.apiUrl ?? "");
  const [apiKey, setApiKey] = useState((server as any)?.apiKey ?? "");
  const [testConnectionResult, setTestConnectionResult] = useState<{
    success: boolean;
    message?: string;
    workflows?: any[];
  } | null>(null);
  const [isTestingN8n, setIsTestingN8n] = useState(false);

  const testN8nConnection = useAction(api.n8nActions.testConnection);

  const handleSubmit = () => {
    onSave({
      name,
      description,
      apiUrl,
      apiKey: apiKey || undefined,
    });
  };

  const handleTestConnection = async () => {
    if (!apiUrl) {
      setTestConnectionResult({
        success: false,
        message: "Please enter an API URL first"
      });
      return;
    }
    
    setIsTestingN8n(true);
    try {
      const result = await testN8nConnection({
        apiUrl,
        apiKey: apiKey || undefined,
      });
      setTestConnectionResult(result);
    } catch (error) {
      setTestConnectionResult({
        success: false,
        message: `Connection failed: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsTestingN8n(false);
    }
  };

  return (
    <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>
          {server ? "Edit n8n Server" : "Add n8n Server"}
        </DialogTitle>
      </DialogHeader>
      <div className="space-y-4 py-4">
        <div className="space-y-3">
          <Label htmlFor="n8nName" className="text-sm font-medium">Server Name</Label>
          <Input
            id="n8nName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="My n8n Server"
            className="h-10"
          />
        </div>
        
        {/* API fields only now */}
        
        <div className="space-y-3">
          <Label htmlFor="n8nDescription" className="text-sm font-medium">Description (Optional)</Label>
          <Textarea
            id="n8nDescription"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Brief description of what this server does"
            rows={2}
            className="resize-none"
          />
        </div>

        {/* API connection */}

        <div className="space-y-3">
          <Label htmlFor="n8nApiUrl" className="text-sm font-medium">n8n API URL</Label>
          <Input
            id="n8nApiUrl"
            value={apiUrl}
            onChange={(e) => setApiUrl(e.target.value)}
            placeholder="https://your-n8n.com"
            className="h-10"
          />
          <p className="text-xs text-muted-foreground">
            The base URL of your n8n instance (e.g., https://your-n8n.domain)
          </p>
        </div>

        <div className="space-y-3">
          <Label htmlFor="n8nApiKey" className="text-sm font-medium">API Key (Optional)</Label>
          <Input
            id="n8nApiKey"
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="n8n_api_..."
            className="h-10"
          />
          <p className="text-xs text-muted-foreground">
            Your n8n API key for authentication (if required)
          </p>
        </div>

        <div className="flex justify-end">
          <Button 
            variant="outline" 
            onClick={() => void handleTestConnection()}
            disabled={!apiUrl || isTestingN8n}
            size="sm"
          >
            {isTestingN8n ? "Testing..." : "Test Connection"}
          </Button>
        </div>

        {testConnectionResult && (
          <Alert variant={testConnectionResult.success ? "default" : "destructive"}>
            {testConnectionResult.success ? <CheckCircle2 className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
            <AlertTitle>
              {testConnectionResult.success ? "Connection Successful" : "Connection Failed"}
            </AlertTitle>
            <AlertDescription>
              {testConnectionResult.message}
            </AlertDescription>
          </Alert>
        )}

        {testConnectionResult?.success &&
          testConnectionResult.workflows &&
          testConnectionResult.workflows.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Available Workflows</Label>
              <div className="max-h-40 overflow-y-auto space-y-2 rounded-md border p-2">
                {testConnectionResult.workflows?.map((wf) => (
                  <div
                    key={wf.id}
                    className="p-2 rounded-md cursor-pointer hover:bg-accent transition-colors"
                  >
                    <div className="text-sm font-medium truncate">{wf.name}</div>
                    <div className="text-xs text-muted-foreground flex items-center gap-2">
                      <code>{wf.id}</code>
                      {wf.active ? (
                        <span className="text-green-500">Active</span>
                      ) : (
                        <span className="text-amber-500">Inactive</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

        {/* This block was removed */}
        {/*
        <div className="space-y-3">
          <Label htmlFor="n8nWorkflowId" className="text-sm font-medium">Selected Workflow ID</Label>
          <Input
            id="n8nWorkflowId"
            value={workflowId}
            onChange={(e) => setWorkflowId(e.target.value)}
            placeholder="Select a workflow from the list above"
            className="h-10"
          />
        </div>
        */}
      </div>
      
      <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 pt-4">
        <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
          Cancel
        </Button>
        <Button onClick={() => void handleSubmit()} className="w-full sm:w-auto">
          {server ? "Update" : "Save"} Server
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}

export function N8nSection() {
  const servers = useQuery(api.n8nServers.list) ?? [];
  const createServer = useMutation(api.n8nServers.add);
  const updateServer = useMutation(api.n8nServers.update);
  const deleteServer = useMutation(api.n8nServers.remove);
  const toggleServer = useMutation(api.n8nServers.toggle);
  const preferences = useQuery(api.preferences.get);
  const updatePreferences = useMutation(api.preferences.update);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingServer, setEditingServer] = useState<N8nServer | undefined>(
    undefined,
  );

  const handleSave = async (data: Partial<N8nServer>) => {
    try {
      if (editingServer) {
        await updateServer({ id: editingServer._id, ...data });
        toast.success("Server updated successfully!");
      } else {
        const serverId = await createServer(data as any);
        toast.success("Server added.");

        /* ------------------------------------------------------------------
         * Ensure server-specific n8n tool group is enabled in preferences
         * so that the assistant may call them without asking for params.
         * ------------------------------------------------------------------ */
        try {
          const prefs = preferences; // captured via closure
          if (prefs && data.name) {
            const safe = data.name.toLowerCase().replace(/[^a-z0-9]/g, "_");
            const baseToolId = `n8n_${safe}`; // group id used in tool config
            const current = prefs.enabledTools ?? [];
            if (!current.includes(baseToolId)) {
              await updatePreferences({ enabledTools: [...current, baseToolId] });
            }
          }
        } catch (err) {
          console.error("Failed to enable n8n server tools in preferences", err);
        }
      }

      setIsFormOpen(false);
      setEditingServer(undefined);
    } catch (error) {
      toast.error(
        `Failed to ${editingServer ? "update" : "create"} server.`,
      );
      console.error(error);
    }
  };

  const handleDelete = async (id: any) => {
    try {
      await deleteServer({ id });
      toast.success("Server deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete server.");
      console.error(error);
    }
  };

  return (
    <div className="space-y-6">
      <Alert>
        <Server className="h-4 w-4" />
        <AlertTitle>Connect to n8n</AlertTitle>
        <AlertDescription>
          Integrate your n8n workflows with ErzenAI to automate tasks. You can
          use webhooks for simple triggers or the n8n API for more complex
          integrations.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Your n8n Servers</CardTitle>
              <CardDescription>
                Manage your connected n8n servers.
              </CardDescription>
            </div>
            <Button
              onClick={() => {
                setEditingServer(undefined);
                setIsFormOpen(true);
              }}
            >
              <Plus className="mr-2 h-4 w-4" /> Add Server
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {servers.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No n8n servers configured yet.
            </div>
          ) : (
            <div className="space-y-4">
              {servers.map((server) => (
                <Card key={server._id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {server.name}
                          <Badge variant={server.isEnabled ? "default" : "secondary"}>
                            {server.isEnabled ? "Enabled" : "Disabled"}
                          </Badge>
                        </CardTitle>
                        <CardDescription>{server.description}</CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setEditingServer(server);
                            setIsFormOpen(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={server.isEnabled ? "destructive" : "default"}
                          size="sm"
                          onClick={() => void toggleServer({ id: server._id })}
                        >
                          {server.isEnabled ? "Disable" : "Enable"}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            void handleDelete(server._id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div>
                        API
                      </div>
                      {server.apiUrl && (
                        <div>
                          API URL: <code className="break-all">{server.apiUrl}</code>
                        </div>
                      )}
                      {/* no workflow ID for server-level entry */}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <N8nServerForm
          server={editingServer}
          onSave={(data) => void handleSave(data)}
          onClose={() => setIsFormOpen(false)}
        />
      </Dialog>
    </div>
  );
} 