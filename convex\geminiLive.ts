"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";

// The official Google GenAI SDK for JavaScript
import { GoogleGenAI, Modality } from "@google/genai";
import { <PERSON>uff<PERSON> } from "buffer";

/**
 * voiceReply – Generate spoken audio for a given text prompt using
 * Gemini 2.5 Flash Live (native-audio dialog model). The response is
 * returned as Base64-encoded 16-bit PCM at 24 kHz so the client can
 * turn it into an <audio> element or AudioBuffer and play it directly.
 */
export const voiceReply = action({
  args: {
    text: v.string(),
  },
  // Returning an object keeps the door open for extra metadata later
  returns: v.object({
    audioBase64: v.string(),
  }),
  handler: async (_ctx, args) => {
    const apiKey = process.env.GOOGLE_GENERATIVE_AI_API_KEY;
    if (!apiKey) {
      throw new Error(
        "Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable. Please add it to your Convex deployment settings."
      );
    }

    // Initialise the SDK
    const ai = new GoogleGenAI({ apiKey });

    // Half-cascade live model – streams 24 kHz audio out
    const model = "gemini-2.0-flash-live-001";

    const config: any = {
      // Ask Live API for audio output
      responseModalities: [Modality.AUDIO],
      systemInstruction:
        "You are a helpful, friendly assistant. Answer succinctly and clearly.",
    } as const;

    // Prepare an array to collect audio chunks
    const pcmChunks: Buffer[] = [];

    // Promise that resolves when we detect the model has completed its turn
    let turnResolved: () => void = () => {};
    const turnDone = new Promise<void>((resolve) => {
      turnResolved = resolve;
    });

    // Establish Live API connection with callbacks so we can capture streaming messages
    const session: any = await ai.live.connect({
      model,
      config,
      callbacks: {
        onopen: () => {},
        onerror: (e: any) => {
          console.error("Live API error", e);
          turnResolved();
        },
        onclose: () => {
          // Safety: ensure promise resolves if socket closes early
          turnResolved();
        },
        onmessage: (msg: any) => {
          try {
            if (msg?.data) {
              // Individual audio chunk is base64-encoded PCM16
              pcmChunks.push(Buffer.from(msg.data, "base64"));
            }

            if (msg?.serverContent?.turnComplete) {
              turnResolved();
            }
          } catch (err) {
            console.error("onmessage processing error", err);
          }
        },
      },
    });

    // Send the user prompt as a complete turn, prompting the model to speak
    await session.sendClientContent({
      turns: [{ role: "user", parts: [{ text: args.text }] }],
      turnComplete: true,
    } as any);

    // Wait until we receive the full response turn (resolved in callback)
    await turnDone;

    await session.close();

    if (pcmChunks.length === 0) {
      throw new Error("Live API session returned no audio data");
    }

    // Concatenate all chunks and re-encode as base64 for transport
    const audioBuffer = Buffer.concat(pcmChunks);
    return { audioBase64: audioBuffer.toString("base64") };
  },
});
