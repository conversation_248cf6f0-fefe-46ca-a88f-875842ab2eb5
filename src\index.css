@import "tailwindcss";
@config "../tailwind.config.cjs";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 1rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */

    --secondary: 215 28% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 28% 17%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 28% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 216 34% 17%;
  }

  .theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 217 91% 60%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 217 30% 96%;
    --secondary-foreground: 217 91% 25%;
    --muted: 217 30% 94%;
    --muted-foreground: 217 30% 40%;
    --card: 217 30% 99%;
    --card-foreground: 217 91% 10%;
    --popover: 217 30% 99%;
    --popover-foreground: 217 91% 10%;
    --border: 217 30% 90%;
    --input: 217 30% 90%;
  }

  .dark.theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 217 91% 60%;
    --accent: 217 45% 20%;
    --accent-foreground: 217 91% 80%;
    --secondary: 217 30% 15%;
    --secondary-foreground: 217 91% 80%;
    --muted: 217 30% 12%;
    --muted-foreground: 217 30% 65%;
    --card: 217 45% 6%;
    --card-foreground: 217 91% 85%;
    --popover: 217 45% 6%;
    --popover-foreground: 217 91% 85%;
    --border: 217 30% 20%;
    --input: 217 30% 20%;
  }

  .theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 142 71% 45%;
    --accent: 142 71% 45%;
    --accent-foreground: 0 0% 0%;
    --secondary: 142 30% 96%;
    --secondary-foreground: 142 71% 25%;
    --muted: 142 30% 94%;
    --muted-foreground: 142 30% 40%;
    --card: 142 30% 99%;
    --card-foreground: 142 71% 10%;
    --popover: 142 30% 99%;
    --popover-foreground: 142 71% 10%;
    --border: 142 30% 90%;
    --input: 142 30% 90%;
  }

  .dark.theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 142 71% 45%;
    --accent: 142 45% 20%;
    --accent-foreground: 142 71% 80%;
    --secondary: 142 30% 15%;
    --secondary-foreground: 142 71% 80%;
    --muted: 142 30% 12%;
    --muted-foreground: 142 30% 65%;
    --card: 142 45% 6%;
    --card-foreground: 142 71% 85%;
    --popover: 142 45% 6%;
    --popover-foreground: 142 71% 85%;
    --border: 142 30% 20%;
    --input: 142 30% 20%;
  }

  .theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 0%;
    --ring: 263 85% 70%;
    --accent: 263 85% 70%;
    --accent-foreground: 0 0% 0%;
    --secondary: 263 30% 96%;
    --secondary-foreground: 263 85% 35%;
    --muted: 263 30% 94%;
    --muted-foreground: 263 30% 40%;
    --card: 263 30% 99%;
    --card-foreground: 263 85% 10%;
    --popover: 263 30% 99%;
    --popover-foreground: 263 85% 10%;
    --border: 263 30% 90%;
    --input: 263 30% 90%;
  }

  .dark.theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 263 85% 70%;
    --accent: 263 45% 20%;
    --accent-foreground: 263 85% 80%;
    --secondary: 263 30% 15%;
    --secondary-foreground: 263 85% 80%;
    --muted: 263 30% 12%;
    --muted-foreground: 263 30% 65%;
    --card: 263 45% 6%;
    --card-foreground: 263 85% 85%;
    --popover: 263 45% 6%;
    --popover-foreground: 263 85% 85%;
    --border: 263 30% 20%;
    --input: 263 30% 20%;
  }

  .theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 0%;
    --ring: 25 95% 53%;
    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 0%;
    --secondary: 25 30% 96%;
    --secondary-foreground: 25 95% 25%;
    --muted: 25 30% 94%;
    --muted-foreground: 25 30% 40%;
    --card: 25 30% 99%;
    --card-foreground: 25 95% 10%;
    --popover: 25 30% 99%;
    --popover-foreground: 25 95% 10%;
    --border: 25 30% 90%;
    --input: 25 30% 90%;
  }

  .dark.theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 25 95% 53%;
    --accent: 25 45% 20%;
    --accent-foreground: 25 95% 80%;
    --secondary: 25 30% 15%;
    --secondary-foreground: 25 95% 80%;
    --muted: 25 30% 12%;
    --muted-foreground: 25 30% 65%;
    --card: 25 45% 6%;
    --card-foreground: 25 95% 85%;
    --popover: 25 45% 6%;
    --popover-foreground: 25 95% 85%;
    --border: 25 30% 20%;
    --input: 25 30% 20%;
  }

  .theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 330 81% 60%;
    --accent: 330 81% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 330 30% 96%;
    --secondary-foreground: 330 81% 30%;
    --muted: 330 30% 94%;
    --muted-foreground: 330 30% 40%;
    --card: 330 30% 99%;
    --card-foreground: 330 81% 10%;
    --popover: 330 30% 99%;
    --popover-foreground: 330 81% 10%;
    --border: 330 30% 90%;
    --input: 330 30% 90%;
  }

  .dark.theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 330 81% 60%;
    --accent: 330 45% 20%;
    --accent-foreground: 330 81% 80%;
    --secondary: 330 30% 15%;
    --secondary-foreground: 330 81% 80%;
    --muted: 330 30% 12%;
    --muted-foreground: 330 30% 65%;
    --card: 330 45% 6%;
    --card-foreground: 330 81% 85%;
    --popover: 330 45% 6%;
    --popover-foreground: 330 81% 85%;
    --border: 330 30% 20%;
    --input: 330 30% 20%;
  }

  .theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 173 80% 40%;
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 0%;
    --secondary: 173 30% 96%;
    --secondary-foreground: 173 80% 20%;
    --muted: 173 30% 94%;
    --muted-foreground: 173 30% 40%;
    --card: 173 30% 99%;
    --card-foreground: 173 80% 10%;
    --popover: 173 30% 99%;
    --popover-foreground: 173 80% 10%;
    --border: 173 30% 90%;
    --input: 173 30% 90%;
  }

  .dark.theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 173 80% 40%;
    --accent: 173 45% 20%;
    --accent-foreground: 173 80% 80%;
    --secondary: 173 30% 15%;
    --secondary-foreground: 173 80% 80%;
    --muted: 173 30% 12%;
    --muted-foreground: 173 30% 65%;
    --card: 173 45% 6%;
    --card-foreground: 173 80% 85%;
    --popover: 173 45% 6%;
    --popover-foreground: 173 80% 85%;
    --border: 173 30% 20%;
    --input: 173 30% 20%;
  }

  .theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 0 84% 60%;
    --accent: 0 84% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 0 30% 96%;
    --secondary-foreground: 0 84% 30%;
    --muted: 0 30% 94%;
    --muted-foreground: 0 30% 40%;
    --card: 0 30% 99%;
    --card-foreground: 0 84% 10%;
    --popover: 0 30% 99%;
    --popover-foreground: 0 84% 10%;
    --border: 0 30% 90%;
    --input: 0 30% 90%;
  }

  .dark.theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 0 84% 60%;
    --accent: 0 45% 20%;
    --accent-foreground: 0 84% 80%;
    --secondary: 0 30% 15%;
    --secondary-foreground: 0 84% 80%;
    --muted: 0 30% 12%;
    --muted-foreground: 0 30% 65%;
    --card: 0 45% 6%;
    --card-foreground: 0 84% 85%;
    --popover: 0 45% 6%;
    --popover-foreground: 0 84% 85%;
    --border: 0 30% 20%;
    --input: 0 30% 20%;
  }

  .theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 0%;
    --ring: 231 48% 48%;
    --accent: 231 48% 48%;
    --accent-foreground: 0 0% 0%;
    --secondary: 231 30% 96%;
    --secondary-foreground: 231 48% 24%;
    --muted: 231 30% 94%;
    --muted-foreground: 231 30% 40%;
    --card: 231 30% 99%;
    --card-foreground: 231 48% 10%;
    --popover: 231 30% 99%;
    --popover-foreground: 231 48% 10%;
    --border: 231 30% 90%;
    --input: 231 30% 90%;
  }

  .dark.theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 231 48% 48%;
    --accent: 231 45% 20%;
    --accent-foreground: 231 48% 80%;
    --secondary: 231 30% 15%;
    --secondary-foreground: 231 48% 80%;
    --muted: 231 30% 12%;
    --muted-foreground: 231 30% 65%;
    --card: 231 45% 6%;
    --card-foreground: 231 48% 85%;
    --popover: 231 45% 6%;
    --popover-foreground: 231 48% 85%;
    --border: 231 30% 20%;
    --input: 231 30% 20%;
  }

  .voice-outline::before {
    content: "";
    position: fixed;
    inset: 0;
    padding: 3px; /* Siri-like outline thickness */
    border-radius: 0.5rem;
    background: conic-gradient(
      from 0deg at 50% 50%,
      #FF2D55, /* Pink */
      #FF9500, /* Orange */
      #FFCC00, /* Yellow */
      #34C759, /* Green */
      #007AFF, /* Blue */
      #5856D6, /* Purple */
      #FF2D55  /* Pink again */
    );
    z-index: 100;
    pointer-events: none;
    /* Create a hollow effect so the gradient shows as a border */
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    animation: siriColorShift 3s linear infinite;
  }
  
  @keyframes siriColorShift {
    0% {
      background: conic-gradient(
        from 0deg at 50% 50%,
        #FF2D55, /* Pink */
        #FF9500, /* Orange */
        #FFCC00, /* Yellow */
        #34C759, /* Green */
        #007AFF, /* Blue */
        #5856D6, /* Purple */
        #FF2D55  /* Pink again */
      );
    }
    100% {
      background: conic-gradient(
        from 360deg at 50% 50%,
        #FF2D55, /* Pink */
        #FF9500, /* Orange */
        #FFCC00, /* Yellow */
        #34C759, /* Green */
        #007AFF, /* Blue */
        #5856D6, /* Purple */
        #FF2D55  /* Pink again */
      );
    }
  }

  /* Prebuilt Extra Themes */
  .theme-sunset {
    --primary: 14 90% 57%;
    --primary-foreground: 0 0% 0%;
    --ring: 14 90% 57%;
    --accent: 14 90% 57%;
    --accent-foreground: 0 0% 0%;
    --secondary: 20 30% 96%;
    --secondary-foreground: 14 90% 30%;
    --muted: 20 30% 94%;
    --muted-foreground: 20 30% 40%;
    --card: 20 30% 99%;
    --card-foreground: 14 90% 10%;
    --popover: 20 30% 99%;
    --popover-foreground: 14 90% 10%;
    --border: 20 30% 90%;
    --input: 20 30% 90%;
  }

  .dark.theme-sunset {
    --primary: 14 90% 57%;
    --primary-foreground: 0 0% 0%;
    --ring: 14 90% 57%;
    --accent: 14 60% 20%;
    --accent-foreground: 14 90% 80%;
    --secondary: 20 30% 15%;
    --secondary-foreground: 14 90% 80%;
    --muted: 20 30% 12%;
    --muted-foreground: 20 30% 65%;
    --card: 14 50% 6%;
    --card-foreground: 14 90% 85%;
    --popover: 14 50% 6%;
    --popover-foreground: 14 90% 85%;
    --border: 20 30% 20%;
    --input: 20 30% 20%;
  }

  .theme-ocean {
    --primary: 195 85% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 195 85% 45%;
    --accent: 195 85% 45%;
    --accent-foreground: 0 0% 0%;
    --secondary: 195 30% 96%;
    --secondary-foreground: 195 85% 25%;
    --muted: 195 30% 94%;
    --muted-foreground: 195 30% 40%;
    --card: 195 30% 99%;
    --card-foreground: 195 85% 10%;
    --popover: 195 30% 99%;
    --popover-foreground: 195 85% 10%;
    --border: 195 30% 90%;
    --input: 195 30% 90%;
  }

  .dark.theme-ocean {
    --primary: 195 85% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 195 85% 45%;
    --accent: 195 60% 18%;
    --accent-foreground: 195 85% 80%;
    --secondary: 195 30% 15%;
    --secondary-foreground: 195 85% 80%;
    --muted: 195 30% 12%;
    --muted-foreground: 195 30% 65%;
    --card: 195 45% 6%;
    --card-foreground: 195 85% 85%;
    --popover: 195 45% 6%;
    --popover-foreground: 195 85% 85%;
    --border: 195 30% 20%;
    --input: 195 30% 20%;
  }

  .theme-forest {
    --primary: 140 55% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 140 55% 40%;
    --accent: 140 55% 40%;
    --accent-foreground: 0 0% 0%;
    --secondary: 140 20% 96%;
    --secondary-foreground: 140 55% 25%;
    --muted: 140 20% 94%;
    --muted-foreground: 140 20% 35%;
    --card: 140 20% 99%;
    --card-foreground: 140 55% 10%;
    --popover: 140 20% 99%;
    --popover-foreground: 140 55% 10%;
    --border: 140 20% 90%;
    --input: 140 20% 90%;
  }

  .dark.theme-forest {
    --primary: 140 55% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 140 55% 40%;
    --accent: 140 35% 18%;
    --accent-foreground: 140 55% 80%;
    --secondary: 140 20% 15%;
    --secondary-foreground: 140 55% 80%;
    --muted: 140 20% 12%;
    --muted-foreground: 140 20% 65%;
    --card: 140 35% 6%;
    --card-foreground: 140 55% 85%;
    --popover: 140 35% 6%;
    --popover-foreground: 140 55% 85%;
    --border: 140 20% 20%;
    --input: 140 20% 20%;
  }

  .theme-gold {
    --primary: 45 85% 55%;
    --primary-foreground: 0 0% 0%;
    --ring: 45 85% 55%;
    --accent: 45 85% 55%;
    --accent-foreground: 0 0% 0%;
    --secondary: 45 30% 96%;
    --secondary-foreground: 45 85% 25%;
    --muted: 45 30% 94%;
    --muted-foreground: 45 30% 40%;
    --card: 45 30% 99%;
    --card-foreground: 45 85% 10%;
    --popover: 45 30% 99%;
    --popover-foreground: 45 85% 10%;
    --border: 45 30% 90%;
    --input: 45 30% 90%;
  }

  .dark.theme-gold {
    --primary: 45 85% 55%;
    --primary-foreground: 0 0% 0%;
    --ring: 45 85% 55%;
    --accent: 45 50% 20%;
    --accent-foreground: 45 85% 80%;
    --secondary: 45 30% 15%;
    --secondary-foreground: 45 85% 80%;
    --muted: 45 30% 12%;
    --muted-foreground: 45 30% 65%;
    --card: 45 45% 6%;
    --card-foreground: 45 85% 85%;
    --popover: 45 45% 6%;
    --popover-foreground: 45 85% 85%;
    --border: 45 30% 20%;
    --input: 45 30% 20%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  
  /* Beautiful text selection styling */
  ::selection {
    background: hsl(var(--primary) / 0.25);
    color: inherit;
    text-shadow: none;
  }
  
  ::-moz-selection {
    background: hsl(var(--primary) / 0.25);
    color: inherit;
    text-shadow: none;
  }
  
  /* Enhanced selection for code blocks and special elements */
  code ::selection,
  pre ::selection,
  .prose ::selection {
    background: hsl(var(--primary) / 0.35);
    color: inherit;
    text-shadow: none;
  }
  
  code ::-moz-selection,
  pre ::-moz-selection,
  .prose ::-moz-selection {
    background: hsl(var(--primary) / 0.35);
    color: inherit;
    text-shadow: none;
  }
  
  /* Dark mode adjustments */
  .dark ::selection {
    background: hsl(var(--primary) / 0.3);
    color: inherit;
  }
  
  .dark ::-moz-selection {
    background: hsl(var(--primary) / 0.3);
    color: inherit;
  }
  
  .dark code ::selection,
  .dark pre ::selection,
  .dark .prose ::selection {
    background: hsl(var(--primary) / 0.4);
    color: inherit;
  }
  
  .dark code ::-moz-selection,
  .dark pre ::-moz-selection,
  .dark .prose ::-moz-selection {
    background: hsl(var(--primary) / 0.4);
    color: inherit;
  }

  /* Ensure cursor pointer on interactive elements */
  button, 
  [role="button"],
  a[href],
  .btn-primary,
  .btn-secondary,
  .btn-accent,
  .auth-button {
    @apply cursor-pointer;
  }
  
  .markdown-content a {
    @apply cursor-pointer underline hover:text-primary transition-colors;
  }

  /* Enhanced focus states for accessibility */
  button:focus,
  [role="button"]:focus,
  a[href]:focus,
  input:focus,
  textarea:focus,
  .focus-ring:focus {
    outline: none;
    ring: 2px;
    ring-color: hsl(var(--primary));
    ring-offset: 2px;
    ring-offset-color: hsl(var(--background));
    transition: all 0.2s ease;
  }
}

.accent-text {
  @apply text-slate-600;
}

body {
  font-family:
    "Inter Variable",
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-shadow shadow-sm hover:shadow;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-4 py-3 rounded bg-primary text-primary-foreground font-semibold hover:bg-primary/90 transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Enhanced theme-aware scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted)) hsl(var(--background));
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

*::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Theme-aware focus rings */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Enhanced card styling */
.themed-card {
  @apply bg-card text-card-foreground border border-border/50 shadow-sm transition-all duration-200 hover:shadow-md;
}

/* Enhanced button variants */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 border border-primary transition-all duration-200 hover:shadow-md active:scale-95;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border transition-all duration-200 hover:shadow-md active:scale-95;
}

.btn-accent {
  @apply bg-accent text-accent-foreground hover:bg-accent/80 border border-border transition-all duration-200 hover:shadow-md active:scale-95;
}

/* Enhanced input styling */
.themed-input {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply transition-all duration-200 focus:shadow-md;
}

/* Markdown Content Styles */
.markdown-content {
  @apply text-foreground;
}

/* Ensure list styles are visible */
.markdown-content ul {
  list-style-type: disc;
  list-style-position: outside;
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.markdown-content ol {
  list-style-type: decimal;
  list-style-position: outside;
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.markdown-content li {
  padding-left: 0.5rem;
  margin-bottom: 0.25rem;
  line-height: 1.6;
}

.markdown-content li::marker {
  color: hsl(var(--primary) / 0.8);
}

/* Nested lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

/* Task lists (checkboxes) */
.markdown-content input[type="checkbox"] {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Blockquotes */
.markdown-content blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1.5rem;
  padding-right: 1rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  background: linear-gradient(to right, hsl(var(--muted) / 0.5), hsl(var(--muted) / 0.2));
  border-radius: 0 0.75rem 0.75rem 0;
  position: relative;
  overflow: hidden;
}

.markdown-content blockquote::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, hsl(var(--primary) / 0.05), transparent);
  pointer-events: none;
}

/* Tables */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.markdown-content thead {
  background: linear-gradient(to right, hsl(var(--muted) / 0.6), hsl(var(--muted) / 0.4));
}

.markdown-content th,
.markdown-content td {
  padding: 1rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.markdown-content th {
  font-weight: 600;
  font-size: 0.875rem;
}

.markdown-content tbody tr:hover {
  background-color: hsl(var(--muted) / 0.3);
}

/* Code blocks */
.markdown-content pre {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.markdown-content code {
  background-color: hsl(var(--muted) / 0.8);
  color: hsl(var(--foreground));
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  border: 1px solid hsl(var(--border) / 0.4);
}

/* Inline code in pre blocks shouldn't have extra styling */
.markdown-content pre code {
  background: transparent;
  border: none;
  padding: 0;
}

/* Headings */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  scroll-margin-top: 1rem;
}

/* Links */
.markdown-content a {
  word-break: break-word;
}

/* Definition lists */
.markdown-content dl {
  margin: 1.5rem 0;
}

.markdown-content dt {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.markdown-content dd {
  margin-left: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--muted-foreground));
}

/* Details and summary */
.markdown-content details {
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.markdown-content details[open] > summary {
  margin-bottom: 0.75rem;
}

.markdown-content summary {
  font-weight: 500;
  cursor: pointer;
  color: hsl(var(--primary));
  transition: color 0.2s ease;
}

.markdown-content summary:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Horizontal rules */
.markdown-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, hsl(var(--border)), transparent);
  margin: 2rem 0;
}

/* Mark, kbd, abbr */
.markdown-content mark {
  background-color: hsl(45 100% 85% / 0.5);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.dark .markdown-content mark {
  background-color: hsl(45 100% 25% / 0.2);
}

.markdown-content kbd {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 0.25rem;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.markdown-content abbr {
  border-bottom: 1px dotted hsl(var(--muted-foreground));
  cursor: help;
}

/* Sup and sub */
.markdown-content sup,
.markdown-content sub {
  font-size: 0.75rem;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.markdown-content sup {
  top: -0.5em;
}

.markdown-content sub {
  bottom: -0.25em;
}

@layer utilities {
  /* Gradient animation for fancy text backgrounds */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 6s ease-in-out infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Organic blob movement for background decoration */
  .animate-blob {
    animation: blob 20s infinite;
  }

  @keyframes blob {
    0%, 100% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
  }

  /* Extra glow animation for orbs/borders */
  .animate-orb {
    animation: orbGlow 6s ease-in-out infinite;
  }

  @keyframes orbGlow {
    0% {
      filter: hue-rotate(0deg);
    }
    50% {
      filter: hue-rotate(180deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }
}

@layer components {
  /* Vision-inspired liquid glass surface that adapts to theme */
  .liquid-glass {
    /* Layout */
    @apply relative overflow-hidden rounded-3xl backdrop-blur-2xl shadow-2xl border border-white/30 dark:border-white/10;

    /* Light theme tint */
    background: rgba(255, 255, 255, 0.45);

    /* Enhanced glass shadow */
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.05),
      0 8px 32px rgba(0, 0, 0, 0.12);
    /* Improve readability of foreground content */
    -webkit-backdrop-filter: blur(18px) saturate(160%) contrast(110%);
    backdrop-filter: blur(18px) saturate(160%) contrast(110%);
  }

  /* Dark theme tint override */
  .dark .liquid-glass {
    background: rgba(255, 255, 255, 0.08);
  }

  /* Subtle inner glow that mimics light refraction on glass */
  .liquid-glass::before {
    content: "";
    position: absolute;
    inset: 0;
    pointer-events: none;
    background: radial-gradient(ellipse at 50% 0%, rgba(255,255,255,0.6) 0%, transparent 70%);
    mix-blend-mode: overlay;
  }

  /* Liquid-like glow behind the glass */
  .liquid-glass::after {
    content: "";
    position: absolute;
    z-index: -1;
    inset: -40%; /* extend beyond bounds to accent blur */
    background: radial-gradient(circle at 50% 50%, hsl(var(--primary) / 0.35) 0%, transparent 70%);
    filter: blur(40px);
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
  .dark .liquid-glass::after {
    opacity: 0.5;
  }
}

/* UI Themes */
.ui-rounded {
  --radius: 1.5rem;
}

.ui-square {
  --radius: 0.25rem;
}

.ui-glass {
  --card: hsla(var(--card) / 0.65);
  --popover: hsla(var(--popover) / 0.65);
  --background: hsla(var(--background) / 0.6);
  backdrop-filter: blur(12px);
}
@layer components {
  /* --- Improved Glassmorphism Theme --- */
  .ui-glass {
    --glass-backdrop-filter: blur(20px) saturate(150%);
    --glass-border-color: hsl(var(--border) / 0.25);
    --glass-shadow: 0 10px 40px hsl(var(--primary) / 0.15);
    
    /* Adjusted backgrounds for better visibility */
    --glass-bg-card: hsl(var(--card) / 0.6);
    --glass-bg-popover: hsl(var(--popover) / 0.7);
    
    --glass-tab-bar-bg: hsl(var(--muted) / 0.15);
    --glass-tab-bar-border: hsl(var(--border) / 0.25);
    --glass-tab-active-bg: hsl(var(--primary) / 0.15);
    --glass-tab-active-border: hsl(var(--primary));
  }
  
  .dark.ui-glass {
    --glass-backdrop-filter: blur(24px) saturate(160%);
    --glass-border-color: hsl(var(--border) / 0.15);
    --glass-shadow: 0 10px 40px hsl(var(--primary) / 0.1);
    
    --glass-bg-card: hsl(var(--card) / 0.15);
    --glass-bg-popover: hsl(var(--popover) / 0.2);
    
    --glass-tab-bar-bg: hsl(var(--muted) / 0.25);
  }
  
  /* Add subtle hover effect for glass components */
  .ui-glass .bg-card:hover,
  .ui-glass .themed-card:hover,
  .ui-glass .bg-popover:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    box-shadow: 0 12px 48px hsl(var(--primary) / 0.2) !important;
  }
  
  /*
    Now, we apply these variables to the standard .bg-card and .bg-popover classes.
    This means any component using these utilities will automatically become glass
    when the .ui-glass theme is enabled. No more manual class changes!
  */
  .ui-glass .bg-card,
  .ui-glass .themed-card,
  .ui-glass .bg-popover {
    background-color: var(--glass-bg-popover) !important;
    border-color: var(--glass-border-color) !important;
    backdrop-filter: var(--glass-backdrop-filter);
    -webkit-backdrop-filter: var(--glass-backdrop-filter);
    box-shadow: var(--glass-shadow) !important;
  }
  .ui-glass .bg-card,
  .ui-glass .themed-card {
    background-color: var(--glass-bg-card) !important;
  }

  /* --- Tab styling within glass components --- */
  .ui-glass .glass-tab-bar {
    background-color: var(--glass-tab-bar-bg);
    border-bottom-color: var(--glass-tab-bar-border);
  }
  .ui-glass .glass-tab-active {
    background-color: var(--glass-tab-active-bg);
    border-bottom-color: var(--glass-tab-active-border);
  }
}

/* UI Themes */
.ui-rounded {
  --radius: 1.5rem;
}

.ui-square {
  --radius: 0.25rem;
}

/* This is now just a marker class. The magic happens in the @layer above. */
.ui-glass {}
