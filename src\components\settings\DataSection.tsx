import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Upload, Download, Trash2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

export function DataSection() {
  const instructionsText = useQuery(api.userInstructions.get);
  const updateInstructions = useMutation(api.userInstructions.update);
  const memories = useQuery(api.userMemories.list);
  const deleteMemory = useMutation(api.userMemories.remove);
  const deleteAllMemories = useMutation(api.userMemories.removeAll);
  
  // Import/Export functionality
  const importConversation = useMutation(api.conversations.importConversation);
  const exportAllConversations = useQuery(api.conversations.exportAllConversations);

  const [currentInstructions, setCurrentInstructions] = useState("");
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (instructionsText !== undefined) {
      setCurrentInstructions(instructionsText);
    }
  }, [instructionsText]);

  const handleSaveInstructions = async () => {
    try {
      await updateInstructions({ instructions: currentInstructions });
      toast.success("Custom instructions saved.");
    } catch (error) {
      toast.error("Failed to save instructions.");
      console.error(error);
    }
  };

  const handleDeleteMemory = async (memoryId: Id<"userMemories">) => {
    try {
      await deleteMemory({ id: memoryId });
      toast.success("Memory deleted.");
    } catch (error) {
      toast.error("Failed to delete memory.");
      console.error(error);
    }
  };

  const handleDeleteAllMemories = async () => {
    try {
      await deleteAllMemories();
      toast.success("All memories deleted.");
    } catch (error) {
      toast.error("Failed to delete memories.");
      console.error(error);
    }
  };

  const handleImportConversations = async () => {
    if (!importFile) return;
    
    setIsImporting(true);
    try {
      const text = await importFile.text();
      const importData = JSON.parse(text);
      const result = await importConversation({ exportData: importData });
      
      setImportFile(null);
      
      if (typeof result === 'object' && result !== null && 'importedConversations' in result) {
        toast.success(`Successfully imported ${result.importedConversations} conversations!`);
      } else {
        toast.success("Successfully imported conversation!");
      }
    } catch (error: any) {
      console.error("Failed to import conversation:", error);
      toast.error(`Failed to import: ${error.message || 'Invalid file format'}`);
    } finally {
      setIsImporting(false);
    }
  };

  const handleExportConversations = async () => {
    if (!exportAllConversations) return;
    
    setIsExporting(true);
    try {
      const blob = new Blob([JSON.stringify(exportAllConversations, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-conversations-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("Conversations exported successfully!");
    } catch (error) {
      console.error("Failed to export conversations:", error);
      toast.error("Failed to export conversations.");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Custom Instructions</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Provide custom instructions for the AI to follow in every conversation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 p-6">
          <Textarea
            value={currentInstructions}
            onChange={(e) => setCurrentInstructions(e.target.value)}
            rows={8}
            placeholder="e.g., I am a software engineer working in Python and TypeScript..."
            className="min-h-[120px] resize-vertical"
          />
          <Button onClick={() => {
            void handleSaveInstructions();
          }} className="w-full sm:w-auto">
            Save Instructions
          </Button>
        </CardContent>
      </Card>

      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">Import & Export Conversations</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Import a previously exported conversation JSON file or export all your conversations for backup.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 p-6">
          <div className="space-y-2">
            <Label htmlFor="import-file">Select File</Label>
            <Input
              id="import-file"
              type="file"
              accept=".json"
              onChange={(e) => setImportFile(e.target.files?.[0] || null)}
              disabled={isImporting}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
            />
          </div>
          
          {importFile && (
            <Alert className="shadow-sm">
              <AlertTitle>File Ready to Import</AlertTitle>
              <AlertDescription>
                <p>Selected file: {importFile.name}</p>
                <p className="text-sm text-muted-foreground">{(importFile.size / 1024).toFixed(1)} KB</p>
              </AlertDescription>
            </Alert>
          )}

          {exportAllConversations && (
            <Alert className="shadow-sm">
              <AlertTitle>Ready to Export</AlertTitle>
              <AlertDescription>
                Found <strong>{exportAllConversations.totalConversations}</strong> conversations to export.
              </AlertDescription>
            </Alert>
          )}

          <Separator className="my-4" />
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => void handleImportConversations()}
              disabled={!importFile || isImporting}
              variant="outline"
              className="w-full sm:w-auto text-sm py-2 px-4 hover:shadow-md transition-shadow"
            >
              {isImporting ? (
                "Importing..."
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </>
              )}
            </Button>

            <Button
              onClick={() => void handleExportConversations()}
              disabled={!exportAllConversations || isExporting}
              variant="outline"
              className="w-full sm:w-auto text-sm py-2 px-4 hover:shadow-md transition-shadow"
            >
              {isExporting ? (
                "Exporting..."
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export All
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-md border border-border/20 rounded-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold">AI Memory</CardTitle>
          <CardDescription className="text-sm text-muted-foreground mt-1">
            Manage the AI's memories from your conversations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <span className="text-sm text-muted-foreground">
              {memories?.length || 0} memories stored
            </span>
            {memories && memories.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => void handleDeleteAllMemories()}
                className="w-full sm:w-auto"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete All
              </Button>
            )}
          </div>

          {memories && memories.length > 0 ? (
            <div className="space-y-2 max-h-96 overflow-y-auto pr-2">
              {memories.map((memory) => (
                <div
                  key={memory._id}
                  className="p-4 rounded-lg flex flex-col sm:flex-row justify-between items-start gap-4 hover:bg-muted transition-colors duration-200 border border-border/50"
                >
                  <div className="flex-1">
                    <p className="text-sm">{memory.memory}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Created: {new Date(memory._creationTime).toLocaleDateString()}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => void handleDeleteMemory(memory._id)}
                    className="text-destructive hover:text-destructive/80"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">No memories stored yet.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 