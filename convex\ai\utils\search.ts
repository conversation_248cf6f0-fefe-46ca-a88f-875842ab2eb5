"use node";

import { api } from "../../_generated/api";

// Helper function to perform Exa.ai search
// Create an asynchronous research task and wait for completion
export async function performExaResearch(
  ctx: any,
  instructions: string,
  waitSeconds = 20
): Promise<string> {
  try {
    const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
      provider: "exa",
    });

    const apiKey =
      apiKeyRecord?.apiKey?.trim() || process.env.EXA_API_KEY || "";
    if (!apiKey) return "Research unavailable – configure your Exa API key.";

    // Create research task
    const createResp = await fetch("https://api.exa.ai/research/v0/tasks", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
      },
      body: JSON.stringify({
        instructions,
        model: "exa-research",
      }),
    });
    if (!createResp.ok) {
      const errorText = await createResp.text();
      throw new Error(
        `Exa research create error: ${createResp.status} - ${errorText}`
      );
    }
    const { id } = await createResp.json();
    if (!id) return "Failed to start research task.";

    const startTime = Date.now();
    const maxWaitTime = 5 * 60 * 1000; // 5 minutes maximum
    let pollCount = 0;

    while (true) {
      const elapsedTime = Date.now() - startTime;

      // Check for maximum timeout
      if (elapsedTime > maxWaitTime) {
        return `Research timed out after ${Math.round(elapsedTime / 1000)}s. Task ID: ${id}`;
      }

      pollCount++;

      try {
        const poll = await fetch(`https://api.exa.ai/research/v0/tasks/${id}`, {
          headers: { "x-api-key": apiKey },
        });

        if (!poll.ok) {
          const errorText = await poll.text();
          throw new Error(
            `Exa research poll error: ${poll.status} - ${errorText}`
          );
        }

        const task = await poll.json();

        // Log the task status for debugging
        console.log(
          `Research poll ${pollCount}: status=${task.status}, elapsed=${Math.round(elapsedTime / 1000)}s`
        );

        if (task.status === "completed" || task.status === "complete") {
          // compute credits: count citations urls if present
          let urlCount = 0;
          if (task.citations) {
            for (const key of Object.keys(task.citations)) {
              const arr = task.citations[key];
              if (Array.isArray(arr)) urlCount += arr.length;
            }
          }
          const creditsUsed = urlCount + 2;
          const body = task.data
            ? JSON.stringify(task.data, null, 2)
            : task.markdown || "Research complete but no data returned.";

          console.log(
            `Research completed after ${Math.round(elapsedTime / 1000)}s, ${pollCount} polls`
          );
          const result = `Research completed. (credits used: ${creditsUsed})\n\n${body}`;
          console.log("Research returning result:", result);
          return result;
        }

        if (task.status === "failed") {
          const errorMsg = task.error || "Unknown error";
          return `Research task failed: ${errorMsg}`;
        }

        // For any other status, continue polling
      } catch (pollError) {
        console.error(`Research poll ${pollCount} failed:`, pollError);
        // Continue polling unless it's been too long
        if (elapsedTime > maxWaitTime / 2) {
          throw pollError;
        }
      }

      // Wait before next poll - increase interval slightly over time
      const waitTime = Math.min(2000 + pollCount * 100, 5000);
      await new Promise((r) => setTimeout(r, waitTime));
    }
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : String(err);
    console.error("Research error:", errorMsg);
    return `Research error: ${errorMsg}`;
  }
}

export async function performExaSearch(
  ctx: any,
  query: string,
  _searchDepth: "basic" | "advanced" = "basic",
  shouldCountUsage: boolean = true
): Promise<string> {
  try {
    const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
      provider: "exa",
    });

    let apiKey = "";
    let usingUserKey = false;

    // PRIORITIZE USER'S API KEY FIRST
    if (apiKeyRecord?.apiKey && apiKeyRecord.apiKey.trim().length > 0) {
      apiKey = apiKeyRecord.apiKey.trim();
      usingUserKey = true;
    } else {
      // Use built-in Exa key as fallback
      apiKey = process.env.EXA_API_KEY || "";
    }

    if (!apiKey) {
      return `Search results for "${query}": Web search unavailable – please configure your Exa API key.`;
    }

    // Only count usage if using built-in key
    if (!usingUserKey && shouldCountUsage) {
      const usage = await ctx.runQuery(api.usage.get);
      const limits = await ctx.runQuery(api.usage.getLimits);

      if (usage && usage.searchesUsed >= limits.searches) {
        return `Search limit reached (${limits.searches}). Add your own Tavily API key in settings for unlimited searches.`;
      }

      await ctx.runMutation(api.usage.incrementSearches);
    }

    const response = await fetch("https://api.exa.ai/search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
      },
      body: JSON.stringify({
        query,
        text: true,
        // additional parameters can be added here when Exa API supports them
      }),
    });

    if (!response.ok) {
      throw new Error(`Exa API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.results && data.results.length > 0) {
      const results = data.results
        .map(
          (result: any) =>
            `${result.title}: ${result.text || result.content || ""} (${result.url})`
        )
        .join("\n\n");

      return `Search results for "${query}":\n\n${results}`;
    }

    return `No results found for "${query}"`;
  } catch (error) {
    return `Search failed for "${query}": ${error instanceof Error ? error.message : "Unknown error"}`;
  }
}
