"use node";

import { getModelInfo } from "../../../src/lib/models";
import { createWebSearchTool, createDeepSearchTool, createResearchTool } from "./webSearch";
import { createWeatherTool } from "./weather";
import { createDateTimeTool } from "./datetime";
import { createCalculatorTool } from "./calculator";
import { createThinkingTool } from "./thinking";
import { createMemoryTool } from "./memory";
import { createUrlFetchTool } from "./urlFetch";
import { createCodeAnalysisTool } from "./codeAnalysis";
import { createImageGenerationTool } from "./imageGeneration";
import { createMCPTools, getMCPToolInfo } from "./mcp";
import { canvasTool } from "./canvas";
import { createN8nTools, getN8nToolInfo } from "./n8n";
import {
  createN8nGenericTools,
  getN8nGenericToolInfo,
  createN8nServerTools,
  getN8nServerToolInfo,
} from "./n8n_tools";
import { Doc } from "../../_generated/dataModel";
import { createContextTool } from "./context";

// Export all tool creators
export {
  createWebSearchTool,
  createDeepSearchTool,
  createResearchTool,
  createWeatherTool,
  createDateTimeTool,
  createCalculatorTool,
  createThinkingTool,
  createMemoryTool,
  createUrlFetchTool,
  createCodeAnalysisTool,
  createImageGenerationTool,
  createMCPTools,
  getMCPToolInfo,
  canvasTool,
  createN8nTools,
  getN8nToolInfo,
};

// Helper function to create tools based on enabled tools and model capabilities
export async function createAvailableTools(
  ctx: any,
  enabledTools: string[],
  model: string,
  usingUserKey: boolean,
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
): Promise<Record<string, any>> {
  // Check if the selected model supports tools
  const modelInfo = getModelInfo(model);
  const modelSupportsTools = modelInfo.supportsTools;

  // Create tools based on enabled tools (only if model supports them)
  const availableTools: Record<string, any> = {};

  // If model doesn't support tools but user has tools enabled, return empty tools
  if (!modelSupportsTools && enabledTools.length > 0) {
    return availableTools; // Return empty tools object
  }

  // Only create tools if model supports them
  if (!modelSupportsTools) {
    return availableTools;
  }

  if (enabledTools.includes("web_search")) {
    availableTools.web_search = createWebSearchTool(ctx, usingUserKey);
  }

  if (enabledTools.includes("research")) {
    availableTools.research = createResearchTool(ctx, usingUserKey);
  }

  if (enabledTools.includes("deep_search")) {
    availableTools.deep_search = createDeepSearchTool(ctx, usingUserKey);
  }

  if (enabledTools.includes("weather")) {
    availableTools.weather = createWeatherTool(ctx);
  }

  if (enabledTools.includes("datetime")) {
    availableTools.datetime = createDateTimeTool();
  }

  if (enabledTools.includes("calculator")) {
    availableTools.calculator = createCalculatorTool();
  }

  if (enabledTools.includes("thinking")) {
    availableTools.thinking = createThinkingTool();
  }

  if (enabledTools.includes("memory")) {
    availableTools.memory = createMemoryTool(ctx);
  }

  if (enabledTools.includes("url_fetch")) {
    availableTools.url_fetch = createUrlFetchTool(ctx);
  }

  if (enabledTools.includes("code_analysis")) {
    availableTools.code_analysis = createCodeAnalysisTool();
  }

  if (enabledTools.includes("image_generation")) {
    availableTools.image_generation = createImageGenerationTool(ctx);
  }

  if (enabledTools.includes("canvas")) {
    availableTools.canvas = canvasTool;
  }

  if (enabledTools.includes("conversation_context")) {
    availableTools.conversation_context = createContextTool(ctx);
  }

  // Add MCP tools if servers are provided
  if (mcpServers && mcpServers.length > 0) {
    try {
      const mcpTools = await createMCPTools(mcpServers);
      Object.assign(availableTools, mcpTools);
    } catch (error) {
      console.error("Failed to create MCP tools:", error);
    }
  }

  // Add n8n server-bound tools if any servers are enabled
  if (n8nServers && n8nServers.length > 0) {
    try {
      const serverTools = createN8nServerTools(n8nServers);
      Object.assign(availableTools, serverTools);
    } catch (err) {
      console.error("Failed to create bound n8n server tools:", err);
    }
  }

  // Add n8n workflow tools if provided (legacy support)
  if (n8nWorkflows && n8nWorkflows.length > 0) {
    try {
      const n8nTools = createN8nTools(n8nWorkflows);
      Object.assign(availableTools, n8nTools);
    } catch (error) {
      console.error("Failed to create n8n workflow tools:", error);
    }
  }

  // Expose generic n8n tools (parameterised) only if there are NO enabled servers
  if (!n8nServers || n8nServers.length === 0) {
    try {
      const genericN8nTools = createN8nGenericTools();
      Object.assign(availableTools, genericN8nTools);
    } catch (error) {
      console.error("Failed to create generic n8n tools:", error);
    }
  }

  return availableTools;
}

// Tool configuration for UI
export const TOOL_CONFIGS = {
  web_search: {
    id: "web_search",
    name: "Web Search",
    description: "Search the web for current information",
    requiresApiKey: "tavily",
    category: "search",
  },
  deep_search: {
    id: "deep_search",
    name: "Deep Search",
    description: "Comprehensive research with multiple queries",
    requiresApiKey: "exa",
    category: "search",
    premium: false,
  },
  research: {
    id: "research",
    name: "Research",
    description: "Autonomous deep research via Exa.ai",
    requiresApiKey: "exa",
    category: "search",
    premium: false,
  },
  weather: {
    id: "weather",
    name: "Weather",
    description: "Get current weather information",
    requiresApiKey: "openweather",
    category: "information",
  },
  datetime: {
    id: "datetime",
    name: "Date & Time",
    description: "Get current date and time information",
    requiresApiKey: null,
    category: "information",
  },
  calculator: {
    id: "calculator",
    name: "Calculator",
    description: "Perform mathematical calculations",
    requiresApiKey: null,
    category: "computation",
  },
  thinking: {
    id: "thinking",
    name: "Thinking",
    description: "Think through complex problems step by step",
    requiresApiKey: null,
    category: "reasoning",
  },
  memory: {
    id: "memory",
    name: "Memory",
    description: "Store and retrieve conversation memory",
    requiresApiKey: null,
    category: "utility",
  },
  url_fetch: {
    id: "url_fetch",
    name: "URL Fetch",
    description: "Fetch content from URLs to analyze or summarize",
    requiresApiKey: null,
    category: "utility",
  },
  code_analysis: {
    id: "code_analysis",
    name: "Code Analysis",
    description: "Analyze code for issues, improvements, or explanations",
    requiresApiKey: null,
    category: "development",
  },
  image_generation: {
    id: "image_generation",
    name: "Image Generation",
    description: "Generate images from text descriptions using AI",
    requiresApiKey: "cloudflare",
    category: "creative",
  },
  canvas: {
    id: "canvas",
    name: "Canvas",
    description:
      "Create interactive markdown documents or code projects with live preview",
    requiresApiKey: null,
    category: "creative",
  },
  conversation_context: {
    id: "conversation_context",
    name: "Conversation Context",
    description:
      "Semantic search across all of the user's conversations (optionally filter to one)",
    requiresApiKey: null,
    category: "search",
  },
} as const;

export type ToolId = keyof typeof TOOL_CONFIGS;

// Function to get available tools configuration
export function getAvailableToolsConfig(
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
): typeof TOOL_CONFIGS & Record<string, any> {
  const baseConfig = { ...TOOL_CONFIGS };

  // Add MCP server tool configs
  if (mcpServers && mcpServers.length > 0) {
    const mcpConfigs = getMCPToolInfo(mcpServers);
    mcpConfigs.forEach((config) => {
      (baseConfig as any)[config.id] = config;
    });
  }

  // Add n8n server tool configs (one per enabled server)
  if (n8nServers && n8nServers.length > 0) {
    const serverInfos = getN8nServerToolInfo(n8nServers);
    serverInfos.forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  // Legacy: add n8n workflow tool configs
  if (n8nWorkflows && n8nWorkflows.length > 0) {
    const n8nInfos = getN8nToolInfo(n8nWorkflows);
    n8nInfos.forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  // Add generic n8n tool configs ONLY if no servers configured
  if (!n8nServers || n8nServers.length === 0) {
    getN8nGenericToolInfo().forEach((info) => {
      (baseConfig as any)[info.id] = info;
    });
  }

  return baseConfig as typeof TOOL_CONFIGS & Record<string, any>;
}

// Create tools based on enabled configuration
export async function createTools(
  ctx: any,
  enabledTools: string[] = [],
  mcpServers: any[] = [],
  n8nServers: any[] = [],
  n8nWorkflows: Doc<"n8nWorkflows">[] = []
) {
  const tools: Record<string, any> = {};

  // Add base tools as needed
  if (enabledTools.includes("web_search")) {
    Object.assign(tools, createWebSearchTool(ctx, false));
  }

  if (enabledTools.includes("calculator")) {
    Object.assign(tools, createCalculatorTool());
  }

  if (enabledTools.includes("datetime")) {
    Object.assign(tools, createDateTimeTool());
  }

  if (enabledTools.includes("weather")) {
    Object.assign(tools, createWeatherTool(ctx));
  }

  if (enabledTools.includes("memory")) {
    Object.assign(tools, createMemoryTool(ctx));
  }

  if (enabledTools.includes("thinking")) {
    Object.assign(tools, createThinkingTool());
  }

  if (enabledTools.includes("canvas")) {
    Object.assign(tools, canvasTool);
  }

  if (enabledTools.includes("url_fetch")) {
    Object.assign(tools, createUrlFetchTool(ctx));
  }

  if (enabledTools.includes("image_generation")) {
    Object.assign(tools, createImageGenerationTool(ctx));
  }

  if (enabledTools.includes("code_analysis")) {
    Object.assign(tools, createCodeAnalysisTool());
  }

  // Add MCP tools if any are enabled
  const mcpToolIds = mcpServers
    .filter((s) => s.isEnabled)
    .map((s) => `mcp_${s.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}`);

  if (mcpToolIds.some((id) => enabledTools.includes(id))) {
    try {
      const mcpTools = await createMCPTools(
        mcpServers.filter((s) => s.isEnabled)
      );

      // Only add enabled MCP tools
      Object.entries(mcpTools).forEach(([key, value]) => {
        const toolBaseId = key.split("_").slice(0, 2).join("_");
        if (enabledTools.includes(toolBaseId)) {
          tools[key] = value;
        }
      });
    } catch (error) {
      console.error("Failed to create MCP tools:", error);
    }
  }

  // Add server-bound n8n tools if provided
  if (n8nServers && n8nServers.length > 0) {
    try {
      const serverTools = createN8nServerTools(n8nServers);
      Object.entries(serverTools).forEach(([key, value]) => {
        const baseId = key.split("_", 3).slice(0, 2).join("_"); // e.g., n8n_myserver
        if (enabledTools.includes(baseId)) {
          tools[key] = value;
        }
      });
    } catch (err) {
      console.error("Failed to create n8n server tools:", err);
    }
  }

  // Legacy workflow tools
  if (n8nWorkflows && n8nWorkflows.length > 0) {
    try {
      const n8nTools = createN8nTools(n8nWorkflows);
      Object.entries(n8nTools).forEach(([key, value]) => {
        if (enabledTools.includes(key)) {
          tools[key] = value;
        }
      });
    } catch (error) {
      console.error("Failed to create n8n workflow tools:", error);
    }
  }

  if (enabledTools.includes("conversation_context")) {
    tools.conversation_context = createContextTool(ctx);
  }

  return tools;
}
