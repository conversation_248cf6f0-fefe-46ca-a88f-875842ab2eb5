import { SignInForm } from "../SignInForm";
import { ParticlesBackground } from "./ParticlesBackground";

export function LoginPage() {
  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-background via-background/95 to-muted/40 overflow-hidden">
      {/* Additional gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-tr from-primary/8 via-transparent to-accent/8 opacity-60"></div>
      {/* Layer 1: The animated background */}
      <div className="absolute inset-0 z-0">
        <ParticlesBackground />
      </div>

      {/* Layer 2: The content, layered on top */}
      <div className="relative z-10 flex flex-col min-h-screen">
        <main className="flex-1 flex items-center justify-center p-4">
          <div className="w-full max-w-md mx-auto text-center">
            <h1 className="text-5xl font-bold text-foreground mb-4">
              Welcome Back
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Sign in to continue to ErzenAI
            </p>
            <SignInForm />
          </div>
        </main>
      </div>
    </div>
  );
} 