# 🧠 ErzenAI - The Ultimate Open-Source AI Chat Platform

> 🚧 **Beta Build Notice**: You are viewing the **development/beta** branch. This version includes rapid, post-hackathon improvements and is **not** the exact build submitted for the T3 Chat Cloneathon. The `main` branch remains frozen at the original submission **because contest submissions can no longer be updated**.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2FErzenXz%2Ferzen-ai&project-name=ErzenAI&env=VITE_CONVEX_URL,CONVEX_DEPLOY_KEY)

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/ErzenXz/erzen-ai#VITE_CONVEX_URL=YourValue&CONVEX_DEPLOY_KEY=YourValue)

[![DeepWiki Docs](https://img.shields.io/badge/DeepWiki-Docs-blue)](https://deepwiki.com/ErzenXz/erzen-ai)

> **ErzenAI** is a revolutionary, open-source AI chat platform that unifies the power of multiple AI providers into a single, elegant interface. Built for developers, teams, and AI enthusiasts who demand flexibility, performance, and complete control over their AI interactions.

## 🌟 Why ErzenAI?

ErzenAI isn't just another AI chat app—it's a comprehensive AI platform designed to handle everything from casual conversations to complex reasoning tasks, code analysis, and creative projects. Whether you're a developer debugging code, a researcher analyzing data, or a creative professional generating content, ErzenAI adapts to your needs.

### 🚀 Key Highlights

- **75+ AI Models**: Access to cutting-edge models from OpenAI, Google, Anthropic, Groq, and more
- **Advanced Reasoning**: Native support for thinking/reasoning models like o1, o3, Claude, and Gemini
- **Real-time Streaming**: Lightning-fast response streaming with optimistic UI updates
- **Powerful Tools**: Integrated web search, image generation, code analysis, and more
- **Enterprise-Ready**: Built on Convex for scalability, with real-time collaboration features
- **Self-Hostable**: Complete control with easy deployment on Vercel or your own infrastructure

## ✨ Features Overview

### 🤖 Multi-Provider AI Access

Access the world's most advanced AI models from a single interface:

**Built-in Providers:**

- **OpenAI**: o3-mini, o4-mini, o3, o1, GPT-4.1, GPT-4o series
- **Google AI**: Gemini 2.5 Pro, Gemini 2.0 Flash, Gemini 1.5 series
- **Anthropic**: Claude Sonnet 4.0, Claude Opus 4.0, Claude 3.5 series
- **Groq**: Ultra-fast inference with Llama 3.3, DeepSeek R1, QWQ-32B
- **OpenRouter**: Access to 15+ additional models including free options

**Additional Providers:**

- **DeepSeek**: DeepSeek Chat, DeepSeek Reasoner
- **Grok**: Grok 3 Beta, Grok Vision, Grok 2 series
- **Cohere**: Command-A, Command-R series, Aya models
- **Mistral**: Magistral, Mistral Medium/Small, Pixtral, Codestral

### 🛠️ Advanced AI Tools

**Web Intelligence:**

- **Real-time Web Search**: Tavily-powered search for current information
- **Deep Research**: Multi-query comprehensive research tool
- **URL Fetching**: Analyze and summarize web content

**Creative & Analysis:**

- **Image Generation**: AI-powered image creation via Cloudflare AI, supporting models like _Flux 1 Schnell_, _Flux 1 Dev_, _Stable Diffusion XL_, and _DreamShaper 8_.
- **Code Analysis**: Advanced code review, debugging, and optimization
- **Mathematical Calculator**: Complex computation support

**Productivity:**

- **Smart Memory**: Context-aware conversation memory
- **DateTime Tools**: Current time and date awareness
- **Thinking Tools**: Step-by-step reasoning assistance

### 🎯 Advanced Features

**Conversation Management:**

- **Branching Conversations**: Explore different discussion paths
- **Conversation Sharing**: Secure link sharing with teammates
- **Export Functionality**: Download conversations in multiple formats
- **Pin Important Chats**: Quick access to your most valuable conversations

**Customization & Control:**

- **Custom System Prompts**: Tailor AI behavior to your needs
- **Model-Specific Settings**: Temperature, reasoning effort
- **Favorite Models**: Quick access to your preferred AI models
- **Flexible Tool Configuration**: Enable/disable tools per conversation

**Tool Extensibility:**

- **External Tool Servers (MCP)**: Connect to external tool servers using `HTTP` and `SSE` (Server-Sent Events), allowing for the integration of custom, self-hosted tools.

**User Experience:**

- **Real-time Streaming**: Watch responses appear as they're generated
- **Dark/Light Themes**: Multiple color themes and system theme support
- **Responsive Design**: Perfect on desktop, tablet, and mobile
- **File Attachments**: Support for images, documents, and media files

### 🔒 Security & Privacy

- **Bring Your Own Keys**: Use your own API keys for unlimited access
- **Built-in Key Management**: Generous free tier with fair usage limits
- **Secure Authentication**: OAuth integration with multiple providers
- **Data Protection**: Enterprise-grade encryption and privacy controls
- **Self-Hosting**: Complete data sovereignty when self-deployed

## 💳 Pricing & Usage

### Credit-Based System

ErzenAI uses a fair, transparent credit system for built-in API key usage:

**Free Plan** - Perfect for getting started

- 100 conversation credits/month
- 10 web searches/month
- $1.00 spending limit
- Access to all AI models
- Real-time streaming
- Conversation sharing

**Pro Plan** - For professionals _(Coming Soon)_

- 500 conversation credits/month
- 200 web searches/month
- $8.00 spending limit
- Priority model access
- Advanced features
- Team collaboration

**Ultra Plan** - For teams and power users _(Coming Soon)_

- 2,500 conversation credits/month
- 1,000 web searches/month
- $20.00 spending limit
- Custom AI instructions
- Advanced analytics
- White-label options

### Unlimited Usage

Use your own API keys for unlimited access to any supported provider without credit limitations.

## 📊 ErzenAI vs Alternatives

| Feature                    | ErzenAI           | ChatGPT Plus           | Claude Pro             | Other OSS    |
| -------------------------- | ----------------- | ---------------------- | ---------------------- | ------------ |
| **Multi-Provider Access**  | ✅ 75+ Models     | ❌ OpenAI Only         | ❌ Anthropic Only      | ⚠️ Limited   |
| **Real-time Streaming**    | ✅ All Models     | ✅                     | ✅                     | ⚠️ Basic     |
| **Advanced Reasoning**     | ✅ Native Support | ✅ o1/o3 Models        | ✅ Claude 3.7 Sonnet   | ❌           |
| **Web Search Integration** | ✅ Tavily API     | ✅ Search with Bing    | ✅ Research Mode       | ⚠️ Limited   |
| **Image Generation**       | ✅ Built-in       | ✅ DALL-E Integration  | ❌                     | ❌           |
| **Image Analysis**         | ✅ Multi-modal    | ✅ Vision Capabilities | ✅ Vision Capabilities | ⚠️ Limited   |
| **Code Analysis**          | ✅ Advanced       | ✅ Advanced            | ✅ Advanced            | ⚠️ Basic     |
| **Conversation Branching** | ✅                | ❌                     | ❌                     | ❌           |
| **Tool Integrations**      | ✅ 10+ Built-in   | ⚠️ Limited Plugins     | ✅ MCP Protocol        | ❌           |
| **Self-Hosting**           | ✅ Full Control   | ❌                     | ❌                     | ✅           |
| **API Access**             | ✅ Planned        | ✅                     | ✅                     | ⚠️ Limited   |
| **Monthly Cost**           | **Free-$20**      | $20                    | $20                    | Free         |
| **Usage Limits**           | ✅ Flexible       | ❌ Fixed               | ❌ Fixed               | ✅ Unlimited |

## 🚀 Performance Metrics

- **Response Time**: Sub-2 second average response initiation
- **Streaming Speed**: 50-150 tokens/second depending on provider
- **Uptime**: 99.9% availability on Vercel/Netlify
- **Concurrent Users**: Scales automatically with serverless architecture
- **Model Switching**: Instant provider/model switching without session loss

## 🏗️ Technical Architecture

**Frontend:**

- **React 19** with TypeScript
- **Vite** for blazing-fast development
- **Tailwind CSS** + **Shadcn/ui** for beautiful, responsive design
- **React Markdown** with syntax highlighting and LaTeX support

**Backend & Database:**

- **Convex** for real-time backend and database
- **Convex Auth** for secure authentication
- **Real-time subscriptions** for live updates
- **File storage** with automatic text extraction

**AI Integration:**

- **Vercel AI SDK** for unified AI provider access
- **Native streaming** support for all models
- **Advanced reasoning** middleware for enhanced thinking
- **Tool calling** with comprehensive error handling

**Deployment:**

- **Vercel** optimized with SPA routing
- **Environment-based configuration**
- **Automatic HTTPS** and CDN
- **Scalable serverless architecture**

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18 or later
- **pnpm** (recommended) or npm
- **Convex account** (free)

### 1. Clone & Install

```bash
git clone https://github.com/ErzenXz/erzen-ai.git
cd erzen-ai
pnpm install
```

### 2. Set Up Convex Backend

```bash
# Install Convex CLI if needed
pnpm dlx convex

# Initialize Convex (creates convex.json)
pnpm dlx convex dev
```

Follow the prompts to create a new Convex project or link to an existing one.

### 3. Configure Environment

Create `.env.local`:

```env
VITE_CONVEX_URL=your-convex-deployment-url
```

### 4. Set Up Authentication & API Keys

In your [Convex Dashboard](https://dashboard.convex.dev/):

**Authentication Setup:**

1. Create a GitHub OAuth App at [GitHub Developer Settings](https://github.com/settings/developers)
2. Set Authorization callback URL to: `https://your-convex-url.convex.site/oauth/github/callback`
3. Copy Client ID and Client Secret to your environment variables

**Environment Variables:**

```env
# Authentication (GitHub OAuth)
AUTH_GITHUB_ID=your-github-oauth-client-id
AUTH_GITHUB_SECRET=your-github-oauth-client-secret

# AI Providers (for built-in usage - optional)
OPENAI_API_KEY=your-openai-key
GOOGLE_API_KEY=your-google-ai-key
ANTHROPIC_API_KEY=your-anthropic-key
GROQ_API_KEY=your-groq-key
DEEPSEEK_API_KEY=your-deepseek-key
COHERE_API_KEY=your-cohere-key
MISTRAL_API_KEY=your-mistral-key

# Tool APIs (optional - enables enhanced features)
TAVILY_API_KEY=your-tavily-search-key
OPENWEATHER_API_KEY=your-weather-key
# Cloudflare (for image generation)
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
```

**API Key Providers:**

- **Tavily**: [Get API Key](https://app.tavily.com/) - For web search functionality
- **OpenWeather**: [Get API Key](https://openweathermap.org/api) - For weather tools
- **Cloudflare**: [Find Account ID & create API Token](https://dash.cloudflare.com/?to=/:account/workers-and-pages/ai) - For image generation. You need your Account ID and an API Token with "AI" permissions.

### 5. Run Development Server

```bash
pnpm dev
```

Your app will be available at `http://localhost:5173`

## 🔐 Production Authentication

For deployments, Convex requires you to provide your own cryptographic keys for signing JSON Web Tokens (JWTs). This is a critical security measure to ensure you have full control over your authentication system.

### Generating Your Keys

You can generate the required `JWKS` and `JWT_PRIVATE_KEY` using the `convex` CLI:

```bash
npx convex auth keys
```

This command will generate two files:

- `jwks.json`: Your public JSON Web Key Set.
- `jwt_private_key.pem`: Your private key for signing tokens.

### Storing Your Keys

You need to add these keys as environment variables in your Convex Dashboard.

1.  **Copy the JWKS Key**:

    - Open `jwks.json`.
    - Copy the entire JSON content.
    - In your [Convex Dashboard](https://dashboard.convex.dev/), create a new environment variable named `JWKS` and paste the JSON content as its value.

2.  **Copy the Private Key**:
    - Open `jwt_private_key.pem`.
    - Copy the entire key, including `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----`.
    - In your Convex Dashboard, create a new environment variable named `JWT_PRIVATE_KEY` and paste the key content as its value.

Your final production environment variables should look like this:

```env
# Authentication (GitHub OAuth)
AUTH_GITHUB_ID=your-github-oauth-client-id
AUTH_GITHUB_SECRET=your-github-oauth-client-secret

# Authentication (Convex JWT)
JWKS={"keys":[{"use":"sig","e":"AQAB","kty":"RSA","n":"..."}]}
JWT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# ... other API keys
```

> **Important**: Keep your `jwt_private_key.pem` file secure and do not commit it to version control. The `jwks.json` file can be shared as it contains public information.

## 🌐 Production Deployment

### Deploy to Vercel (Recommended)

1. **Connect Repository**: Import your GitHub repository to Vercel
2. **Environment Variables**: Add your `VITE_CONVEX_URL` in Vercel settings
3. **Deploy**: Vercel automatically detects Vite and configures build settings
4. **Custom Domain**: Add your custom domain in Vercel dashboard

### Deploy to Netlify

1. **Connect Repository**: Import from GitHub
2. **Build Settings**:
   - Build command: `pnpm build`
   - Publish directory: `dist`
3. **Environment Variables**: Add `VITE_CONVEX_URL`
4. **Deploy**: Automatic deployment on every push

### Self-Hosting

```bash
# Build the application
pnpm build

# Serve with any static hosting solution
# The dist/ folder contains all static assets
```

## 🔧 Advanced Configuration

### Model Configuration

Add custom models in `src/lib/models.ts`:

```typescript
export const MODEL_INFO: Record<string, ModelInfo> = {
  "your-custom-model": {
    id: "your-custom-model",
    displayName: "Your Custom Model",
    provider: "your-provider",
    maxInputTokens: 100000,
    maxOutputTokens: 4000,
    contextWindow: 100000,
    pricing: { input: 2.5, output: 10.0 }, // per 1M tokens
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
    capabilities: ["Text", "Code"],
    description: "Your custom model description",
    icon: "🤖",
  },
};
```

### Provider Configuration

Add new AI providers in `convex/ai/providers/constants.ts`:

```typescript
export const PROVIDER_BASE_URLS = {
  // ... existing providers
  your_provider: "https://api.yourprovider.com/v1",
} as const;

export const SUPPORTED_PROVIDERS = [
  // ... existing providers
  "your_provider",
] as const;
```

### Tool Configuration

Enable/disable tools in `convex/ai/tools/index.ts`:

```typescript
export const TOOL_CONFIGS = {
  your_custom_tool: {
    id: "your_custom_tool",
    name: "Your Custom Tool",
    description: "Description of what your tool does",
    requiresApiKey: null, // or "provider-name"
    category: "utility",
  },
};
```

### Theme Customization

Modify themes in `tailwind.config.cjs`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        DEFAULT: "hsl(var(--primary))",
        foreground: "hsl(var(--primary-foreground))",
      },
      // Add your custom colors
    }
  }
}
```

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### Development Setup

```bash
# Fork the repository
git clone your-fork
cd erzen-ai
pnpm install

# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
pnpm dev

# Submit pull request
```

### Contribution Guidelines

- **Code Quality**: Follow TypeScript best practices
- **Testing**: Ensure all features work with multiple AI providers
- **Documentation**: Update README for new features
- **UI/UX**: Maintain consistency with existing design patterns

### Areas for Contribution

- 🤖 **New AI Providers**: Add support for additional AI services
- 🛠️ **Tools**: Create new tools for enhanced AI capabilities
- 🎨 **UI/UX**: Improve user interface and experience
- 📱 **Mobile**: Enhance mobile responsiveness
- 🔒 **Security**: Strengthen authentication and privacy features
- 🌐 **Internationalization**: Add multi-language support

## 📋 Roadmap

### Near Term (Q3 2025)

- [ ] **Team Collaboration**: Multi-user conversation sharing and editing.
- [ ] **Advanced Analytics**: Granular usage statistics and cost-tracking dashboards.
- [ ] **Plugin System**: Initial release of a client-side plugin system for third-party tool integration.
- [ ] **stdio for MCP**: Add support for `stdio`-based external tool servers.

### Mid Term (Q4 2025 - Q1 2026)

- [ ] **API Access**: Public REST API for programmatic access to conversations and AI models.
- [ ] **Enterprise Features**: SSO integration (SAML, OIDC) and advanced admin controls.
- [ ] **Mobile Apps**: Native iOS and Android applications with offline support.
- [ ] **Custom AI Training**: User-friendly interface for fine-tuning models on personal or company data.

### Long Term (Q2 2026 and beyond)

- [ ] **AI Agents**: Develop autonomous agents capable of completing complex, multi-step tasks.
- [ ] **Multi-Modal AI**: Introduce advanced vision, audio, and video processing capabilities.
- [ ] **Workflow Automation**: Build a visual workflow editor to automate AI-powered tasks and connect with external services.
- [ ] **On-Premise Deployment**: Offer a version of ErzenAI that can be deployed on-premise for maximum data security.

## 🔧 Troubleshooting

### Common Issues

**Build/Development Issues:**

```bash
# Clear all caches and reinstall
rm -rf node_modules pnpm-lock.yaml
pnpm install

# Reset Convex
pnpm dlx convex dev --reset
```

**Authentication Problems:**

- Verify GitHub OAuth callback URL matches your Convex deployment
- Check that `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` are correctly set
- Ensure your GitHub OAuth app is active and properly configured

**API Key Issues:**

- Test your API keys directly with provider APIs
- Check rate limits and billing on your provider accounts
- Verify environment variables are set in Convex dashboard, not just locally

**Deployment Issues:**

- Ensure `VITE_CONVEX_URL` is set correctly in your hosting platform
- Check that all environment variables are properly configured
- Verify your Convex project is deployed and accessible

### Performance Optimization

```bash
# Enable production mode
NODE_ENV=production pnpm build

# Optimize bundle size
pnpm dlx vite-bundle-analyzer dist
```

## 🆘 Support & Community

### Getting Help

- 📖 **Documentation**: [View on DeepWiki](https://deepwiki.com/ErzenXz/erzen-ai)
- 💬 **GitHub Discussions**: Community support and feature requests
- 🐛 **Issues**: Bug reports and feature requests
- 📧 **Contact**: Direct support for enterprise users

### Community

- ⭐ **Star the Repository**: Show your support
- 🍴 **Fork & Contribute**: Help improve the platform
- 📢 **Share**: Spread the word about ErzenAI
- 💝 **Sponsor**: Support ongoing development

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

---

## 🎯 Why Choose ErzenAI?

**For Developers:**

- Multi-model access for different coding tasks
- Advanced code analysis and debugging tools
- Real-time collaboration with team members
- Self-hostable for complete control

**For Teams:**

- Unified AI access across your organization
- Conversation sharing and collaboration
- Cost-effective with built-in API key management
- Enterprise-ready security and privacy

**For AI Enthusiasts:**

- Access to the latest AI models and features
- Open-source with active development
- Extensible tool system for custom workflows
- Community-driven feature development

**For Enterprises:**

- Self-hosting options for data sovereignty
- Flexible pricing with your own API keys
- Advanced user management and analytics
- Professional support and customization

---

<div align="center">

**🚀 Ready to experience the future of AI chat?**

[**Get Started Now**](https://erzen-ai.vercel.app) | [**View Demo**](https://erzen-ai.vercel.app/homepage) | [**Join Community**](https://github.com/ErzenXz/erzen-ai/discussions)

Made with ❤️ by Erzen Krasniqi

</div>
